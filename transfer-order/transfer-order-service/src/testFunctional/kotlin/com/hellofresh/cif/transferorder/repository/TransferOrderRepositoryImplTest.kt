package com.hellofresh.cif.transferorder.repository

import FunctionalTest
import com.hellofresh.cif.transfer.order.schema.Tables.TRANSFER_ORDER
import com.hellofresh.cif.transfer.order.schema.Tables.TRANSFER_ORDER_SKUS
import com.hellofresh.cif.transferorder.TransferOrderEntityFixture
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class TransferOrderRepositoryImplTest : FunctionalTest() {

    @Test
    fun `should save new transfer order entity`() {
        val transferOrderEntity = TransferOrderEntityFixture.getTransferOrderEntity()
        runBlocking {
            transferOrderRepository.upsert(transferOrderEntity)
            val transferOrderDbRecord = dsl.select().from(TRANSFER_ORDER).fetch().first()
            assertEquals(
                transferOrderEntity.transferOrderNumber,
                transferOrderDbRecord[TRANSFER_ORDER.TRANSFER_ORDER_NUMBER]
            )
            assertEquals(transferOrderEntity.week, transferOrderDbRecord[TRANSFER_ORDER.WEEK])
            assertEquals(transferOrderEntity.status.name, transferOrderDbRecord[TRANSFER_ORDER.STATUS].toString())
            assertEquals(transferOrderEntity.sourceDc, transferOrderDbRecord[TRANSFER_ORDER.SOURCE_DC])
            assertEquals(transferOrderEntity.destinationDc, transferOrderDbRecord[TRANSFER_ORDER.DESTINATION_DC])

            val transferOrderSkuDbRecord = dsl.select().from(TRANSFER_ORDER_SKUS).fetch().first()
            val expectedTransferOrderSku = transferOrderEntity.skus.first()
            assertEquals(
                expectedTransferOrderSku.transferOrderNumber,
                transferOrderSkuDbRecord[TRANSFER_ORDER_SKUS.TRANSFER_ORDER_NUMBER]
            )
            assertEquals(expectedTransferOrderSku.skuId, transferOrderSkuDbRecord[TRANSFER_ORDER_SKUS.SKU_ID])
            assertEquals(expectedTransferOrderSku.supplierId, transferOrderSkuDbRecord[TRANSFER_ORDER_SKUS.SUPPLIER_ID])
            assertEquals(
                expectedTransferOrderSku.quantity.getValue(),
                transferOrderSkuDbRecord[TRANSFER_ORDER_SKUS.QUANTITY]
            )
        }
    }

    @Test
    fun `should delete transfer order entity`() {
        val transferOrderEntity = TransferOrderEntityFixture.getTransferOrderEntity()
        runBlocking {
            transferOrderRepository.upsert(transferOrderEntity)
            val transferOrderDbRecord = dsl.select().from(TRANSFER_ORDER).fetch().first()
            assertEquals(
                transferOrderEntity.transferOrderNumber,
                transferOrderDbRecord[TRANSFER_ORDER.TRANSFER_ORDER_NUMBER]
            )
            val transferOrderSkuDbRecord = dsl.select().from(TRANSFER_ORDER_SKUS).fetch().first()
            val expectedTransferOrderSku = transferOrderEntity.skus.first()
            assertEquals(
                expectedTransferOrderSku.transferOrderNumber,
                transferOrderSkuDbRecord[TRANSFER_ORDER_SKUS.TRANSFER_ORDER_NUMBER]
            )
            assertEquals(expectedTransferOrderSku.skuId, transferOrderSkuDbRecord[TRANSFER_ORDER_SKUS.SKU_ID])
            assertEquals(expectedTransferOrderSku.supplierId, transferOrderSkuDbRecord[TRANSFER_ORDER_SKUS.SUPPLIER_ID])
            assertEquals(
                expectedTransferOrderSku.quantity.getValue(),
                transferOrderSkuDbRecord[TRANSFER_ORDER_SKUS.QUANTITY]
            )
            transferOrderRepository.delete(transferOrderEntity.transferOrderNumber)
            val deletedTransferOrders = dsl.select().from(TRANSFER_ORDER).fetch()
            assertTrue(deletedTransferOrders.isEmpty())
        }
    }

    @ParameterizedTest
    @MethodSource("getTransferOrderStatus")
    fun `should NOT save new transfer order entity with UNRECOGNIZED, STATE_CANCELLED, STATE_DELETED transfer order status`(
        status: TransferOrderStatus,
    ) {
        val transferOrderEntity = TransferOrderEntityFixture.getTransferOrderEntity().copy(status = status)
        runBlocking {
            transferOrderRepository.upsert(transferOrderEntity)
            val transferOrderDbRecord = dsl.select().from(TRANSFER_ORDER).fetch()
            assertTrue(transferOrderDbRecord.isEmpty())
        }
    }

    @Test
    fun `should save and update transfer order entity`() {
        val transferOrderEntity = TransferOrderEntityFixture.getTransferOrderEntity()
        val destinationDc = "OE"

        runBlocking {
            transferOrderRepository.upsert(transferOrderEntity)
            val updatedEntity = transferOrderEntity.copy(destinationDc = destinationDc)
            transferOrderRepository.upsert(updatedEntity)
            val result = dsl.select().from(TRANSFER_ORDER).fetch().first()
            assertEquals(destinationDc, result[TRANSFER_ORDER.DESTINATION_DC])
        }
    }

    @Test
    fun `should save and update transfer order entity also deleted the non existing skus`() {
        val transferOrderEntity = TransferOrderEntityFixture.getTransferOrderEntity()
        runBlocking {
            transferOrderRepository.upsert(transferOrderEntity)
            val newSkuId = UUID.randomUUID()
            val updatedEntity = transferOrderEntity.copy(
                skus = listOf(
                    TransferOrderEntityFixture.getTransferOrderSkuEntity(skuId = newSkuId),
                ),
            )
            transferOrderRepository.upsert(updatedEntity)
            val result = dsl.select().from(TRANSFER_ORDER_SKUS)
                .where(TRANSFER_ORDER_SKUS.SKU_ID.eq(transferOrderEntity.skus.first().skuId)).fetch()
            assertTrue(result.isEmpty())

            val resultWithNewSku = dsl.select().from(TRANSFER_ORDER_SKUS)
                .where(TRANSFER_ORDER_SKUS.SKU_ID.eq(newSkuId)).fetch()
            assertFalse(resultWithNewSku.isEmpty())
        }
    }

    companion object {
        @JvmStatic
        @Suppress("unused")
        private fun getTransferOrderStatus() = Stream.of(
            Arguments.of(TransferOrderStatus.STATE_DELETED),
            Arguments.of(TransferOrderStatus.STATE_CANCELLED),
            Arguments.of(TransferOrderStatus.UNRECOGNIZED),
        )
    }
}
