package com.hellofresh.cif.transferorder

import FunctionalTest
import InfraPreparation.startKafkaAndCreateTopics
import KafkaInfraPreparation.createKafkaProducer
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.ProcessTransferOrders
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.transfer.order.schema.Tables.TRANSFER_ORDER
import com.hellofresh.cif.transfer.order.schema.Tables.TRANSFER_ORDER_SKUS
import com.hellofresh.cif.transfer.order.schema.tables.records.TransferOrderRecord
import com.hellofresh.cif.transfer.order.schema.tables.records.TransferOrderSkusRecord
import com.hellofresh.cif.transferorder.service.TransferOrderConsumer
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.Duration
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.Serializer
import org.junit.jupiter.api.BeforeAll
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.shaded.org.awaitility.Awaitility

class TransferOrderAppIntegrationTest : FunctionalTest() {

    private val dcConfigRecord1 = createDcConfig("BX")
    private val dcConfigRecord2 = createDcConfig("VE")
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())

    @Test
    fun `should consume from transfer order topic and process records created & deleted`() {
        // given
        statsigFeatureFlagClient.fixtures = setOf(ProcessTransferOrders(setOf(ContextData(MARKET, "DACH"))))
        dsl.batchInsert(dcConfigRecord1, dcConfigRecord2).execute()

        val key = UUID.randomUUID().toString()

        val value = TransferOrderProtoFixture.getProtoTransferOrder()

        val record = ProducerRecord(TOPIC_NAME, key, value)

        // when
        topicProducer.send(record).get()

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                launchProcessor(
                    parallelism = 1,
                    meterRegistry = meterRegistry,
                    transferOrderConsumer = TransferOrderConsumer(
                        transferOrderRepository = transferOrderRepository,
                        statsigFeatureFlagClient = statsigFeatureFlagClient,
                        isLocal = true,
                    ),
                    pollConfig = pollConfig,
                    consumerConfig = consumerConfig,
                    dcConfigService = dcConfigService,
                )
            }

            val transferOrders = mutableListOf<TransferOrderRecord>()
            val transferOrderSkus = mutableListOf<TransferOrderSkusRecord>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(30))
                .until {
                    val transferOrder = dsl.selectFrom(TRANSFER_ORDER).fetch()
                    val transferOrderSku = dsl.selectFrom(TRANSFER_ORDER_SKUS).fetch()
                    transferOrders.addAll(transferOrder)
                    transferOrderSkus.addAll(transferOrderSku)
                    transferOrders.count() == 1
                }

            with(transferOrders.first()) {
                assertEquals(value.transferOrderNumber, transferOrderNumber)
                assertEquals(value.sourceDcCode, sourceDc)
                assertEquals(value.destinationDcCode, destinationDc)
                assertEquals(value.status.name, status.name)
                assertEquals(DcWeek("${value.productionWeek.year}-W${value.productionWeek.week}").value, week)
                assertEquals(value.marketCode, marketCode)
            }

            with(transferOrderSkus.first()) {
                value.itemsList.map { item ->
                    assertEquals(item.skuId, skuId.toString())
                    assertEquals(item.supplierId, supplierId.toString())
                    assertEquals(BigDecimal(item.quantity.value), quantity)
                }
            }

            val deletedTransferOrderRecord = ProducerRecord(
                TOPIC_NAME,
                key,
                TransferOrderProtoFixture.getProtoTransferOrder(
                    id = UUID.fromString(value.id),
                    status = "STATE_CANCELLED",
                ),
            )

            // when
            topicProducer.send(deletedTransferOrderRecord).get()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(30))
                .until {
                    dsl.selectFrom(TRANSFER_ORDER).fetch().size == 0
                }
            app.cancel()
        }
    }

    @Test
    fun `should delete the transfer order if the sku items does not exist`() {
        // given
        statsigFeatureFlagClient.fixtures = setOf(ProcessTransferOrders(setOf(ContextData(MARKET, "DACH"))))
        dsl.batchInsert(dcConfigRecord1, dcConfigRecord2).execute()

        val key = UUID.randomUUID().toString()

        val value = TransferOrderProtoFixture.getProtoTransferOrder()

        val record = ProducerRecord(TOPIC_NAME, key, value)

        // when
        topicProducer.send(record).get()

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                launchProcessor(
                    parallelism = 1,
                    meterRegistry = meterRegistry,
                    transferOrderConsumer = TransferOrderConsumer(
                        transferOrderRepository = transferOrderRepository,
                        statsigFeatureFlagClient = statsigFeatureFlagClient,
                        isLocal = true,
                    ),
                    pollConfig = pollConfig,
                    consumerConfig = consumerConfig,
                    dcConfigService = dcConfigService,
                )
            }

            val transferOrders = mutableListOf<TransferOrderRecord>()
            val transferOrderSkus = mutableListOf<TransferOrderSkusRecord>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(30))
                .until {
                    val transferOrder = dsl.selectFrom(TRANSFER_ORDER).fetch()
                    val transferOrderSku = dsl.selectFrom(TRANSFER_ORDER_SKUS).fetch()
                    transferOrders.addAll(transferOrder)
                    transferOrderSkus.addAll(transferOrderSku)
                    transferOrders.count() == 1
                }

            with(transferOrders.first()) {
                assertEquals(value.transferOrderNumber, transferOrderNumber)
                assertEquals(value.sourceDcCode, sourceDc)
                assertEquals(value.destinationDcCode, destinationDc)
                assertEquals(value.status.name, status.name)
                assertEquals(DcWeek("${value.productionWeek.year}-W${value.productionWeek.week}").value, week)
                assertEquals(value.marketCode, marketCode)
            }

            with(transferOrderSkus.first()) {
                value.itemsList.map { item ->
                    assertEquals(item.skuId, skuId.toString())
                    assertEquals(item.supplierId, supplierId.toString())
                    assertEquals(BigDecimal(item.quantity.value), quantity)
                }
            }

            val deletedTransferOrderRecord = ProducerRecord(
                TOPIC_NAME,
                key,
                TransferOrderProtoFixture.getProtoTransferOrder(
                    id = UUID.fromString(value.id),
                    status = "STATE_RESERVED",
                    withSkus = false,
                ),
            )

            // when
            topicProducer.send(deletedTransferOrderRecord).get()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(30))
                .until {
                    dsl.selectFrom(TRANSFER_ORDER).fetch().size == 0
                }
            app.cancel()
        }
    }

    @Test
    fun `should NOT consume & process from transfer order records if the status is not of STATE_RESERVED, STATE_DELIVERED`() {
        // given
        statsigFeatureFlagClient.fixtures = setOf(ProcessTransferOrders(setOf(ContextData(MARKET, "DACH"))))
        dsl.batchInsert(dcConfigRecord1, dcConfigRecord2).execute()

        val key = UUID.randomUUID().toString()

        val value = TransferOrderProtoFixture.getProtoTransferOrder(
            status = "STATE_OPEN",
        )

        val record = ProducerRecord(TOPIC_NAME, key, value)

        // when
        topicProducer.send(record).get()

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                launchProcessor(
                    parallelism = 1,
                    meterRegistry = meterRegistry,
                    transferOrderConsumer = TransferOrderConsumer(
                        transferOrderRepository = transferOrderRepository,
                        statsigFeatureFlagClient = statsigFeatureFlagClient,
                        isLocal = true,
                    ),
                    pollConfig = pollConfig,
                    consumerConfig = consumerConfig,
                    dcConfigService = dcConfigService,
                )
            }

            val transferOrders = mutableListOf<TransferOrderRecord>()
            val transferOrderSkus = mutableListOf<TransferOrderSkusRecord>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(30))
                .until {
                    val transferOrder = dsl.selectFrom(TRANSFER_ORDER).fetch()
                    val transferOrderSku = dsl.selectFrom(TRANSFER_ORDER_SKUS).fetch()
                    transferOrders.addAll(transferOrder)
                    transferOrderSkus.addAll(transferOrderSku)
                    transferOrders.isEmpty()
                }

            app.cancel()
        }
    }

    @Test
    fun `should NOT consume & process from transfer order records if the market is not enabled`() {
        // given
        statsigFeatureFlagClient.fixtures = setOf(ProcessTransferOrders(setOf(ContextData(MARKET, "US"))))
        dsl.batchInsert(dcConfigRecord1, dcConfigRecord2).execute()

        val key = UUID.randomUUID().toString()

        val value = TransferOrderProtoFixture.getProtoTransferOrder(marketCode = "DACH")

        val record = ProducerRecord(TOPIC_NAME, key, value)

        // when
        topicProducer.send(record).get()

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                launchProcessor(
                    parallelism = 1,
                    meterRegistry = meterRegistry,
                    transferOrderConsumer = TransferOrderConsumer(
                        transferOrderRepository = transferOrderRepository,
                        statsigFeatureFlagClient = statsigFeatureFlagClient,
                        isLocal = true,
                    ),
                    pollConfig = pollConfig,
                    consumerConfig = consumerConfig,
                    dcConfigService = dcConfigService,
                )
            }

            val transferOrders = mutableListOf<TransferOrderRecord>()
            val transferOrderSkus = mutableListOf<TransferOrderSkusRecord>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(30))
                .until {
                    val transferOrder = dsl.selectFrom(TRANSFER_ORDER).fetch()
                    val transferOrderSku = dsl.selectFrom(TRANSFER_ORDER_SKUS).fetch()
                    transferOrders.addAll(transferOrder)
                    transferOrderSkus.addAll(transferOrderSku)
                    transferOrders.isEmpty()
                }

            app.cancel()
        }
    }

    companion object {
        private lateinit var topicProducer: KafkaProducer<String, TransferOrder>
        private lateinit var consumerConfig: Map<String, String>
        private lateinit var pollConfig: PollConfig

        private lateinit var kafka: KafkaContainer

        private val meterRegistry = SimpleMeterRegistry()

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            kafka = startKafkaAndCreateTopics(
                listOf(TOPIC_NAME),
            )

            topicProducer = createKafkaProducer(
                kafka,
                Serializer<String> { _, data -> data.toByteArray() },
                Serializer<TransferOrder> { _, data -> data.toByteArray() },
            )

            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "csku-inventory-forecast.transfer-order-service-test-integration.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )
            pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
            )
        }
    }
}
