package com.hellofresh.cif.transferorder

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.transferorder.model.TransferOrderEntity
import com.hellofresh.cif.transferorder.model.TransferOrderSkuEntity
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID

object TransferOrderEntityFixture {

    @Suppress("LongParameterList")
    fun getTransferOrderEntity(
        transferOrderNumber: String = "2552AET01638",
        sourceDcCode: String = "BL",
        destinationDcCode: String = "AE",
        status: TransferOrderStatus = TransferOrderStatus.STATE_ORDERED,
        skuId: UUID = UUID.randomUUID(),
        supplierId: UUID = UUID.randomUUID(),
    ): TransferOrderEntity =
        TransferOrderEntity(
            transferOrderNumber = transferOrderNumber,
            sourceDc = sourceDcCode,
            destinationDc = destinationDcCode,
            status = status,
            week = "2025-W27",
            deliveryStartTime = OffsetDateTime.now().plusDays(2),
            deliveryEndTime = OffsetDateTime.now().plusDays(2).plusHours(2),
            marketCode = "CA",
            skus = listOf(
                getTransferOrderSkuEntity(
                    supplierId = supplierId,
                    transferOrderNumber = transferOrderNumber,
                    skuId = skuId,
                ),
            ),
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now(),
        )
    fun getTransferOrderSkuEntity(
        skuId: UUID = UUID.randomUUID(),
        supplierId: UUID = UUID.randomUUID(),
        transferOrderNumber: String = UUID.randomUUID().toString(),
        quantity: BigDecimal = BigDecimal("99"),
    ): TransferOrderSkuEntity =
        TransferOrderSkuEntity(
            transferOrderNumber = transferOrderNumber,
            skuId = skuId,
            supplierId = supplierId,
            quantity = SkuQuantity.fromBigDecimal(quantity, SkuUOM.UOM_UNIT),
            lotExpirationTime = null,
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now(),
        )
}
