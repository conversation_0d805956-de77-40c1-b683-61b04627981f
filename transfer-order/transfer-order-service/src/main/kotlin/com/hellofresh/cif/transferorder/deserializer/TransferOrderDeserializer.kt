package com.hellofresh.cif.transferorder.deserializer

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_GAL
import com.hellofresh.cif.models.SkuUOM.UOM_KG
import com.hellofresh.cif.models.SkuUOM.UOM_LBS
import com.hellofresh.cif.models.SkuUOM.UOM_LITRE
import com.hellofresh.cif.models.SkuUOM.UOM_OZ
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.models.SkuUOM.UOM_UNRECOGNIZED
import com.hellofresh.cif.models.SkuUOM.UOM_UNSPECIFIED
import com.hellofresh.cif.transferorder.model.TransferOrderEntity
import com.hellofresh.cif.transferorder.model.TransferOrderSkuEntity
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import com.hellofresh.dateUtil.models.toOffsetDateTimeFromLocalTime
import com.hellofresh.proto.stream.transferOrder.v1.CasePackaging.UOM as TransferOrderUOM
import com.hellofresh.proto.stream.transferOrder.v1.State
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrderItem
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.time.ZoneId
import java.util.UUID
import org.apache.kafka.common.header.Headers
import org.apache.kafka.common.serialization.Deserializer

class TransferOrderDeserializer(private val dcConfigService: DcConfigService) : Deserializer<TransferOrderEntity> {

    override fun deserialize(topic: String?, data: ByteArray?): TransferOrderEntity =
        deserialize(topic, null, data)

    override fun deserialize(topic: String?, headers: Headers?, data: ByteArray?): TransferOrderEntity {
        check(data != null) { "Value can not be null" }
        return with(
            TransferOrder.parseFrom(data),
        ) {
            getZoneId(dcConfigService, id, sourceDcCode, destinationDcCode).let { zoneId ->
                TransferOrderEntity(
                    transferOrderNumber = transferOrderNumber,
                    sourceDc = sourceDcCode,
                    destinationDc = destinationDcCode,
                    status = toTransferOrderStatus(status),
                    week = DcWeek("${productionWeek.year}-W${productionWeek.week}").value,
                    deliveryStartTime = deliveryStartTime.toOffsetDateTimeFromLocalTime(zoneId),
                    deliveryEndTime = deliveryEndTime.toOffsetDateTimeFromLocalTime(zoneId),
                    marketCode = marketCode,
                    skus = itemsList.map { transferOrderSku ->
                        toTransferOrderSku(
                            transferOrderNumber,
                            zoneId,
                            transferOrderSku,
                            createTime.toOffsetDateTimeFromLocalTime(zoneId),
                            updateTime.toOffsetDateTimeFromLocalTime(zoneId),
                        )
                    },
                    createdAt = createTime.toOffsetDateTimeFromLocalTime(zoneId),
                    updatedAt = updateTime.toOffsetDateTimeFromLocalTime(zoneId),
                )
            }
        }
    }

    private fun toTransferOrderStatus(status: State?): TransferOrderStatus =
        when (status) {
            State.STATE_ORDERED -> TransferOrderStatus.STATE_ORDERED
            State.STATE_RESERVED -> TransferOrderStatus.STATE_RESERVED
            State.STATE_DELIVERED -> TransferOrderStatus.STATE_DELIVERED
            State.STATE_CANCELLED -> TransferOrderStatus.STATE_CANCELLED
            State.STATE_DELETED -> TransferOrderStatus.STATE_DELETED
            else -> TransferOrderStatus.UNRECOGNIZED
        }

    private fun toTransferOrderSku(
        transferOrderNumber: String,
        zoneId: ZoneId,
        transferOrderSku: TransferOrderItem,
        createdAt: OffsetDateTime,
        updatedAt: OffsetDateTime,
    ): TransferOrderSkuEntity =
        TransferOrderSkuEntity(
            transferOrderNumber = transferOrderNumber,
            skuId = UUID.fromString(transferOrderSku.skuId),
            supplierId = UUID.fromString(transferOrderSku.supplierId),
            quantity = SkuQuantity.fromBigDecimal(
                BigDecimal(transferOrderSku.quantity.value),
                transferOrderSku.casePackaging.unit.toUOM()
            ),
            lotExpirationTime = transferOrderSku.lotExpirationTime.toOffsetDateTimeFromLocalTime(zoneId),
            createdAt = createdAt,
            updatedAt = updatedAt,
        )

    private fun getZoneId(dcConfigService: DcConfigService, id: String, sourceDcCode: String, destinationDcCode: String): ZoneId =
        dcConfigService.dcConfigurations[sourceDcCode]?.zoneId ?: throw IllegalArgumentException(
            "DC configuration not found for source DC: $sourceDcCode in transfer order, " +
                "destination dc = $destinationDcCode in transfer order: transfer order id = $id",
        )

    private fun TransferOrderUOM.toUOM(): SkuUOM =
        when (this) {
            TransferOrderUOM.UOM_UNIT -> UOM_UNIT
            TransferOrderUOM.UOM_UNSPECIFIED -> UOM_UNSPECIFIED
            TransferOrderUOM.UOM_KG -> UOM_KG
            TransferOrderUOM.UOM_LBS -> UOM_LBS
            TransferOrderUOM.UOM_OZ -> UOM_OZ
            TransferOrderUOM.UOM_GAL -> UOM_GAL
            TransferOrderUOM.UOM_LITRE -> UOM_LITRE
            TransferOrderUOM.UNRECOGNIZED -> UOM_UNRECOGNIZED
        }
}
