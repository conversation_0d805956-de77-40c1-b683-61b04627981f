package com.hellofresh.cif.transferorder.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.transfer.order.schema.Tables.TRANSFER_ORDER
import com.hellofresh.cif.transfer.order.schema.Tables.TRANSFER_ORDER_SKUS
import com.hellofresh.cif.transfer.order.schema.enums.TransferOrderStatus as TransferOrderStatusInDb
import com.hellofresh.cif.transfer.order.schema.enums.Uom
import com.hellofresh.cif.transferorder.model.TransferOrderEntity
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_DELIVERED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_ORDERED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_RESERVED
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.DSLContext

class TransferOrderRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext
) : TransferOrderRepository {
    private val saveTransferOrder = "save-transfer-order"
    private val deleteTransferOrder = "delete-transfer-order"

    override suspend fun upsert(transferOrder: TransferOrderEntity) {
        logger.info("Saving/Updating transfer order: $transferOrder")

        metricsDSLContext.withTagName(saveTransferOrder)
            .transactionAsync { tx ->
                val txDsl = tx.dsl()
                val transferOrderStatus = toTransferOrderStatus(transferOrder.status)
                transferOrderStatus?.let {
                    insertTransferOrder(txDsl, transferOrder, transferOrderStatus)

                    with(createInsertUpdateSkuBatch(txDsl)) {
                        transferOrder.skus.map { toSku ->
                            bind(
                                transferOrder.transferOrderNumber,
                                toSku.skuId,
                                toSku.supplierId,
                                toSku.quantity.getValue(),
                                toUom(SkuUOM.UOM_UNIT), // hard coded to UOM_UNIT for now. Need to fix this later
                                toSku.lotExpirationTime,
                                toSku.quantity.getValue(),
                                toUom(SkuUOM.UOM_UNIT), // hard coded to UOM_UNIT for now. Need to fix this later
                                toSku.lotExpirationTime,
                            )
                        }
                        execute()
                    }
                    txDsl.deleteFrom(TRANSFER_ORDER_SKUS)
                        .where(TRANSFER_ORDER_SKUS.TRANSFER_ORDER_NUMBER.eq(transferOrder.transferOrderNumber))
                        .and(TRANSFER_ORDER_SKUS.SKU_ID.notIn(transferOrder.skus.map { it.skuId }))
                        .execute()
                }
            }.await()
    }

    override suspend fun delete(transferOrderNumber: String) {
        logger.info("Deleting transfer order with id: $transferOrderNumber")

        metricsDSLContext.withTagName(deleteTransferOrder)
            .transactionAsync { tx ->
                val txDsl = tx.dsl()

                txDsl.deleteFrom(
                    TRANSFER_ORDER_SKUS,
                ).where(TRANSFER_ORDER_SKUS.TRANSFER_ORDER_NUMBER.eq(transferOrderNumber)).execute()

                txDsl.deleteFrom(
                    TRANSFER_ORDER
                ).where(TRANSFER_ORDER.TRANSFER_ORDER_NUMBER.eq(transferOrderNumber)).execute()
            }.await()
    }

    private fun insertTransferOrder(
        txDsl: DSLContext,
        transferOrder: TransferOrderEntity,
        transferOrderStatus: TransferOrderStatusInDb?
    ) {
        txDsl.insertInto(TRANSFER_ORDER)
            .columns(
                TRANSFER_ORDER.TRANSFER_ORDER_NUMBER,
                TRANSFER_ORDER.SOURCE_DC,
                TRANSFER_ORDER.DESTINATION_DC,
                TRANSFER_ORDER.STATUS,
                TRANSFER_ORDER.WEEK,
                TRANSFER_ORDER.DELIVERY_START_TIME,
                TRANSFER_ORDER.DELIVERY_END_TIME,
                TRANSFER_ORDER.MARKET_CODE,
            ).values(
                transferOrder.transferOrderNumber,
                transferOrder.sourceDc,
                transferOrder.destinationDc,
                transferOrderStatus,
                transferOrder.week,
                transferOrder.deliveryStartTime,
                transferOrder.deliveryEndTime,
                transferOrder.marketCode,
            ).onDuplicateKeyUpdate()
            .set(TRANSFER_ORDER.TRANSFER_ORDER_NUMBER, transferOrder.transferOrderNumber)
            .set(TRANSFER_ORDER.SOURCE_DC, transferOrder.sourceDc)
            .set(TRANSFER_ORDER.DESTINATION_DC, transferOrder.destinationDc)
            .set(TRANSFER_ORDER.STATUS, transferOrderStatus)
            .set(TRANSFER_ORDER.WEEK, transferOrder.week)
            .set(TRANSFER_ORDER.DELIVERY_START_TIME, transferOrder.deliveryStartTime)
            .set(TRANSFER_ORDER.DELIVERY_END_TIME, transferOrder.deliveryEndTime)
            .set(TRANSFER_ORDER.MARKET_CODE, transferOrder.marketCode)
            .execute()
    }

    private fun createInsertUpdateSkuBatch(dslContext: DSLContext) = dslContext.batch(
        dslContext.insertInto(TRANSFER_ORDER_SKUS)
            .columns(
                TRANSFER_ORDER_SKUS.TRANSFER_ORDER_NUMBER,
                TRANSFER_ORDER_SKUS.SKU_ID,
                TRANSFER_ORDER_SKUS.SUPPLIER_ID,
                TRANSFER_ORDER_SKUS.QUANTITY,
                TRANSFER_ORDER_SKUS.UOM,
                TRANSFER_ORDER_SKUS.LOT_EXPIRATION_TIME,
            ).values(
                "",
                UUID(0, 0),
                UUID(0, 0),
                BigDecimal.ZERO,
                Uom.UOM_UNIT,
                OffsetDateTime.MIN,
            ).onDuplicateKeyUpdate()
            .set(TRANSFER_ORDER_SKUS.QUANTITY, BigDecimal.ZERO)
            .set(TRANSFER_ORDER_SKUS.UOM, Uom.UOM_UNIT)
            .set(TRANSFER_ORDER_SKUS.LOT_EXPIRATION_TIME, OffsetDateTime.MIN),
    )

    private fun toUom(skuUOM: SkuUOM): Uom =
        when (skuUOM) {
            SkuUOM.UOM_UNIT -> Uom.UOM_UNIT
            SkuUOM.UOM_KG -> Uom.UOM_KG
            SkuUOM.UOM_LBS -> Uom.UOM_LBS
            SkuUOM.UOM_OZ -> Uom.UOM_OZ
            SkuUOM.UOM_GAL -> Uom.UOM_GAL
            SkuUOM.UOM_LITRE -> Uom.UOM_LITRE
            SkuUOM.UOM_UNRECOGNIZED, SkuUOM.UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
        }

    private fun toTransferOrderStatus(transferOrderStatus: TransferOrderStatus?) =
        transferOrderStatus?.let {
            when (transferOrderStatus) {
                STATE_RESERVED -> TransferOrderStatusInDb.STATE_RESERVED
                STATE_ORDERED -> TransferOrderStatusInDb.STATE_ORDERED
                STATE_DELIVERED -> TransferOrderStatusInDb.STATE_DELIVERED
                else -> null
            }
        }

    companion object : Logging
}
