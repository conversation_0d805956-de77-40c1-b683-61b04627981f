package com.hellofresh.cif.transferorder

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.config.ConfigurationLoader.isLocal
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategyType.LOG_ERROR_IGNORE
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.transferorder.deserializer.TransferOrderDeserializer
import com.hellofresh.cif.transferorder.repository.TransferOrderRepositoryImpl
import com.hellofresh.cif.transferorder.service.TransferOrderConsumer
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.Executors
import kotlin.time.Duration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.common.serialization.StringDeserializer

private const val HTTP_PORT = 8081
const val TOPIC_NAME = "public.transfer-order.v1"

private fun getParallelismConfig() = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)

suspend fun main() {
    val meterRegistry = createMeterRegistry()
    val parallelism = getParallelismConfig()
    StatusServer.run(
        meterRegistry,
        HTTP_PORT,
    )
    val readMetricsDSLContext = DBConfiguration.jooqReadOnlyDslContext(parallelism, meterRegistry)
    val readWriteMetricsDSLContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)
    val dcConfigService = DcConfigService(meterRegistry, DcRepositoryImpl(readMetricsDSLContext))
    val statsigFeatureFlagClient = statsigFeatureFlagClient()
    val transferOrderRepository = TransferOrderRepositoryImpl(readWriteMetricsDSLContext)
    val transferOrderConsumer = TransferOrderConsumer(
        transferOrderRepository,
        statsigFeatureFlagClient,
        isLocal()
    )
    val consumerConfig =
        ConfigurationLoader.loadKafkaConsumerConfigurations() + mapOf(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false")
    launchProcessor(parallelism, meterRegistry, transferOrderConsumer, consumerConfig, dcConfigService)
}

@Suppress("LongParameterList")
suspend fun launchProcessor(
    parallelism: Int,
    meterRegistry: MeterRegistry,
    transferOrderConsumer: TransferOrderConsumer,
    consumerConfig: Map<String, String>,
    dcConfigService: DcConfigService,
    pollConfig: PollConfig = pollConfig(),
) {
    val coroutineDispatcher = Executors.newFixedThreadPool(
        parallelism,
        ThreadFactoryBuilder().setNameFormat("producer-thread-%d").build(),
    ).asCoroutineDispatcher()

    withContext(coroutineDispatcher) {
        repeat(parallelism) {
            launch {
                shutdownNeeded {
                    CoroutinesProcessor(
                        pollConfig,
                        ConsumerProcessorConfig(
                            consumerConfig,
                            StringDeserializer(),
                            TransferOrderDeserializer(dcConfigService = dcConfigService),
                            listOf(TOPIC_NAME),
                        ),
                        meterRegistry = meterRegistry,
                        process = transferOrderConsumer,
                        handleDeserializationException = DeserializationExceptionStrategy.create(
                            LOG_ERROR_IGNORE,
                            meterRegistry,
                        ),
                        recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                            meterRegistry,
                            "transfer_order_service_write_failure",
                        ),
                    )
                        .also {
                            HealthChecks.add(it)
                            StartUpChecks.add(it)
                        }
                }.run()
            }
        }
    }
}

private fun statsigFeatureFlagClient() = StatsigFactory.build(
    ::shutdownHook,
    sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
    userId = ConfigurationLoader.getStringOrFail("application.name"),
    isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
    hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
)

internal fun pollConfig() = PollConfig(
    Duration.parse(ConfigurationLoader.getStringOrFail("poll.timeout")),
    ConfigurationLoader.getIntegerOrFail("poll.interval_ms").toLong(),
    Duration.parse(ConfigurationLoader.getStringOrFail("process.timeout")),
)
