package com.hellofresh.cif.transferorder.service

import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.ProcessTransferOrders
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.transferorder.model.TransferOrderEntity
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_CANCELLED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_DELETED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_DELIVERED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_ORDERED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_RESERVED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.UNRECOGNIZED
import com.hellofresh.cif.transferorder.repository.TransferOrderRepository
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.logging.log4j.kotlin.Logging

class TransferOrderConsumer(
    private val transferOrderRepository: TransferOrderRepository,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
    private val isLocal: Boolean,
) : suspend (ConsumerRecords<String, TransferOrderEntity>) -> Unit {

    override suspend fun invoke(records: ConsumerRecords<String, TransferOrderEntity>) {
        if (!records.isEmpty) {
            logger.info("Start processing transfer order data , count = ${records.count()}")
            records.asSequence()
                .map { it.value() }
                .filter {
                    it.status != UNRECOGNIZED && (isLocal || isMarketEnabledToProcess(it.marketCode))
                }
                .forEach { record ->
                    runCatching {
                        logger.info(
                            "Processing transfer order data ," +
                                "transferOrderNumber = ${record.transferOrderNumber}, sourceDc = ${record.sourceDc}, " +
                                "destinationDc = ${record.destinationDc}, market = ${record.marketCode}",
                        )
                        if (hasToBeDeleted(record)) {
                            transferOrderRepository.delete(record.transferOrderNumber)
                        } else {
                            transferOrderRepository.upsert(record)
                        }
                    }.onFailure {
                        logger.error(
                            "Failed to process transfer order record:  ${record.transferOrderNumber}",
                            it,
                        )
                    }
                }
        }
    }

    private fun hasToBeDeleted(transferOrderEntity: TransferOrderEntity): Boolean =
        transferOrderEntity.skus.isEmpty() ||
            when (transferOrderEntity.status) {
                STATE_CANCELLED, STATE_DELETED, UNRECOGNIZED -> true
                STATE_ORDERED, STATE_RESERVED, STATE_DELIVERED -> false
            }

    private fun isMarketEnabledToProcess(
        market: String,
    ): Boolean =
        statsigFeatureFlagClient.isEnabledFor(
            ProcessTransferOrders(
                setOf(
                    ContextData(MARKET, market),
                ),
            ),
        )

    companion object : Logging
}
