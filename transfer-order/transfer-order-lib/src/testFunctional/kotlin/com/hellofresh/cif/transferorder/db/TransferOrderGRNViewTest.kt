package com.hellofresh.cif.transferorder.db

import FunctionalTest
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDERS_GRN_VIEW
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull

class TransferOrderGRNViewTest : FunctionalTest() {
    private val sku1Id = UUID.randomUUID()
    private val sku2Id = UUID.randomUUID()
    private val orderNumber = UUID.randomUUID().toString()
    private val orderSku1 = createTestSku(orderNumber, sku1Id)
    private val orderSku2 = createTestSku(orderNumber, sku2Id)
    private val order = createTestOrder(orderNumber, "GR", skus = listOf(orderSku1, orderSku2))

    @BeforeEach
    fun setup() {
        val dc1 = createDC("GR")
        val dc2 = createDC("WW")

        val sku1 = createSkuSpec(sku1Id, "PHF-77-133013-4", "Aardbeien / Fraises (250g)")
        val sku2 = createSkuSpec(sku2Id, "PHF-11-46874-4", "Blauwe bessen - Etiket: Blauwe bessen (250g)")

        insertTransferOrder(listOf(order))
        insertTransferOrderDetails(order.transferOrderNumber, order.skus)
        insertSkuSpecification(listOf(sku1, sku2))
        insertDC(listOf(dc1, dc2))
    }

    @Test
    fun `should return all SKUs with matching GRNs`() {
        val grn1 =
            createGRN(order.destinationDc, order.transferOrderNumber, orderSku1.skuId, orderSku1.quantity.getValue())
        val grn2 =
            createGRN(order.destinationDc, order.transferOrderNumber, orderSku2.skuId, orderSku2.quantity.getValue())

        insertGoodsReceivedNotes(listOf(grn1, grn2))
        refreshTransferOrderView()

        val records = dsl.selectFrom(TRANSFER_ORDERS_GRN_VIEW).fetch()

        assertEquals(2, records.size)

        val toGrn1 = records.find { it["to_sku_id"] == orderSku1.skuId }
        val toGrn2 = records.find { it["to_sku_id"] == orderSku2.skuId }

        assertNotNull(toGrn1)
        assertEquals(order.transferOrderNumber, toGrn1["to_number"])
        assertEquals(order.sourceDc, toGrn1["to_source_dc"])
        assertEquals(order.destinationDc, toGrn1["to_destination_dc"])
        assertEquals(order.week, toGrn1["to_week"])
        assertEquals(order.marketCode, toGrn1["to_market_code"])
        assertEquals(orderSku1.skuId, toGrn1["to_sku_id"])
        assertEquals(orderSku1.skuId, toGrn1["grn_sku_id"])
        assertEquals(orderSku1.quantity.getValue(), toGrn1["grn_quantity"])
        assertEquals(grn1.dcCode, toGrn1["grn_dc_code"])
        assertEquals("CLOSED", toGrn1["grn_delivery_status"])

        assertNotNull(toGrn2)
        assertEquals(order.transferOrderNumber, toGrn2["to_number"])
        assertEquals(order.sourceDc, toGrn2["to_source_dc"])
        assertEquals(order.destinationDc, toGrn2["to_destination_dc"])
        assertEquals(order.week, toGrn2["to_week"])
        assertEquals(order.marketCode, toGrn2["to_market_code"])
        assertEquals(orderSku2.skuId, toGrn2["to_sku_id"])
        assertEquals(orderSku2.skuId, toGrn2["grn_sku_id"])
        assertEquals(orderSku2.quantity.getValue(), toGrn2["grn_quantity"])
        assertEquals(grn2.dcCode, toGrn2["grn_dc_code"])
        assertEquals("CLOSED", toGrn2["grn_delivery_status"])
    }

    @Test
    fun `should return SKU without GRN with null GRN fields`() {
        val grn =
            createGRN(order.destinationDc, order.transferOrderNumber, orderSku1.skuId, orderSku1.quantity.getValue())
        insertGoodsReceivedNotes(listOf(grn))
        refreshTransferOrderView()

        val records = dsl.selectFrom(TRANSFER_ORDERS_GRN_VIEW).fetch()

        assertEquals(2, records.size)

        val skuWithGrn = records.find { it["to_sku_id"] == orderSku1.skuId }
        val skuWithoutGrn = records.find { it["to_sku_id"] == orderSku2.skuId }

        assertNotNull(skuWithGrn)
        assertEquals(orderSku1.skuId, skuWithGrn["grn_sku_id"])
        assertEquals(orderSku1.quantity.getValue(), skuWithGrn["grn_quantity"])

        assertNotNull(skuWithoutGrn)
        assertEquals(null, skuWithoutGrn["grn_sku_id"])
        assertEquals(null, skuWithoutGrn["grn_quantity"])
    }
}
