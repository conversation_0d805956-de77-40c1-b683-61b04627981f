package com.hellofresh.cif.transferorder.db

import FunctionalTest
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER_SKUS
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class TransferOrderRepositoryImplTest : FunctionalTest() {
    private val sku1Id = UUID.randomUUID()
    private val sku2Id = UUID.randomUUID()
    private val sku3Id = UUID.randomUUID()
    private val order1Number = UUID.randomUUID().toString()
    private val order2Number = UUID.randomUUID().toString()
    private val order3Number = "T1003"
    private val order1StartTime = OffsetDateTime.now().minusDays(5)
    private val order1EndTime = OffsetDateTime.now().minusDays(3)

    private val order3StartTime = OffsetDateTime.now().minusDays(3)
    private val order3EndTime = OffsetDateTime.now()
    private val order3GrnDate = OffsetDateTime.now().minusDays(4)
    private val dcGR = createDC("GR")
    private val dcVE = createDC("VE")
    private val dcVB = createDC("VB")
    private val dcBX = createDC("BX")

    @BeforeEach
    fun setup() {
        insertDC(listOf(dcGR, dcVE, dcVB, dcBX))

        // create sku
        val sku1 = createSkuSpec(sku1Id, "PHF-77-133013-4", "Aardbeien / Fraises (250g)")
        val sku2 = createSkuSpec(sku2Id, "PHF-11-46874-4", "Blauwe bessen - Etiket: Blauwe bessen (250g)")
        val sku3 = createSkuSpec(sku3Id, "PHF-11-46874-5", "Blauwe bessen - Etiket: Blauwe bessen (550g)")
        insertSkuSpecification(listOf(sku1, sku2, sku3))

        val order1 = createTestOrder(
            orderNumber = order1Number,
            sourceDc = dcGR.dcCode,
            destinationDc = dcVE.dcCode,
            startTime = order1StartTime,
            endTime = order1EndTime,
            skus = listOf(
                createTestSku(order1Number, sku1.id),
            )
        )

        val order2 = createTestOrder(
            orderNumber = order2Number,
            sourceDc = dcVB.dcCode,
            destinationDc = dcGR.dcCode,
            skus = listOf(createTestSku(order2Number), createTestSku(order2Number, sku2.id))
        )

        // order with GRN
        val order3 = createTestOrder(
            orderNumber = order3Number,
            sourceDc = dcVE.dcCode,
            destinationDc = dcBX.dcCode,
            startTime = order3StartTime,
            endTime = order3EndTime,
            skus = listOf(
                createTestSku(order3Number, sku3.id, quantity = 100),
            )
        )

        insertTransferOrder(listOf(order1, order2, order3))
        insertTransferOrderDetails(order1.transferOrderNumber, order1.skus)
        insertTransferOrderDetails(order2.transferOrderNumber, order2.skus)
        insertTransferOrderDetails(order3.transferOrderNumber, order3.skus)

        // create order3 Grn
        val grn1 = createGRN(
            order3.destinationDc,
            order3.transferOrderNumber,
            order3.skus.first().skuId,
            order3.skus.first().quantity.getValue(),
            deliveryDate = order3GrnDate,
        )
        insertGoodsReceivedNotes(listOf(grn1))

        refreshSkuSpecificationView()
        refreshTransferOrderView()
    }

    @Test
    fun `test findALL returns matching orders`() = runBlocking {
        val result = transferOrderRepository.findAll()

        assertEquals(3, result.size)
        assertEquals("UK", result.first().marketCode)
    }

    @Test
    fun `test findByMarketAndSourceDC returns matching orders`() = runBlocking {
        val result = transferOrderRepository.findByMarketAndSourceDC(marketCode = "UK", sourceDc = "GR")

        assertEquals(1, result.size)
        assertEquals("UK", result.first().marketCode)
        assertEquals("GR", result.first().sourceDc)
    }

    @Test
    fun `test findByMarketAndSourceDC returns empty list if no match`() = runBlocking {
        val result = transferOrderRepository.findByMarketAndSourceDC(marketCode = "US", sourceDc = "VR")
        assertTrue(result.isEmpty())
    }

    @Test
    fun `test save with transferOrderSkus`() = runBlocking {
        val result = dsl.selectFrom(TRANSFER_ORDER_SKUS).fetch()
        assertEquals(4, result.size)
    }

    @Test
    fun `test fetch Inbound Trasnfer Orders`() = runBlocking {
        val dateRange = DateRange(
            fromDate = order1StartTime.toLocalDate(),
            toDate = order1EndTime.toLocalDate(),
        )

        val inboundResult = transferOrderRepository.fetchInboundOrders(
            setOf(dcVE.dcCode),
            dateRange = dateRange
        )

        with(inboundResult.first()) {
            assertEquals("TO-$order1Number", transferOrderNumber)
            assertEquals(dcVE.dcCode, destinationDc)
            assertEquals(1, transferOrderSkus.size)
            assertEquals(sku1Id, transferOrderSkus.first().skuId)
        }

        val inbounds = TransferOrderInbounds(inboundResult)
        val inBoundList = inbounds.inbounds(
            inboundResult.first().transferOrderSkus.first().skuId,
            dcVE.dcCode,
            order1StartTime.toLocalDate()
        )

        assertEquals(0, inBoundList.tos.sumOf { it.deliveries.size })
    }

    @Test
    fun `test fetch Inbound Trasnfer Orders with GRN`() = runBlocking {
        val order3DateRange = DateRange(
            fromDate = order3StartTime.toLocalDate(),
            toDate = order3EndTime.toLocalDate(),
        )

        val inboundResult = transferOrderRepository.fetchInboundOrders(
            dcCodes = setOf(dcBX.dcCode),
            dateRange = order3DateRange
        )

        with(inboundResult.first()) {
            assertEquals("TO-$order3Number", transferOrderNumber)
            assertEquals(dcBX.dcCode, destinationDc)
            assertEquals(1, transferOrderSkus.size)
            assertEquals(sku3Id, transferOrderSkus.first().skuId)
            assertEquals(SkuQuantity.fromLong(100), transferOrderSkus.first().expectedQuantity)
            assertEquals(1, transferOrderSkus.first().deliveries.size)
        }

        val inbounds = TransferOrderInbounds(inboundResult)
        val inBoundList = inbounds.inbounds(
            skuId = inboundResult.first().transferOrderSkus.first().skuId,
            dcCode = dcBX.dcCode, // destination DC
            date = order3GrnDate.toLocalDate()
        )

        // it has 1 inbounds
        assertEquals(1, inBoundList.tos.sumOf { it.deliveries.size })
    }

    @Test
    fun `test fetch Outbound Trasnfer Orders`() = runBlocking {
        val dateRange = DateRange(
            fromDate = order1StartTime.toLocalDate(),
            toDate = order1EndTime.toLocalDate(),
        )

        val outboundResult = transferOrderRepository.fetchOutboundOrders(
            dcCodes = setOf(dcGR.dcCode),
            dateRange = dateRange
        )

        with(outboundResult.first()) {
            assertEquals("TO-$order1Number", transferOrderNumber)
            assertEquals(dcGR.dcCode, sourceDc)
            assertEquals(1, transferOrderSkus.size)
            assertEquals(sku1Id, transferOrderSkus.first().skuId)
        }
    }

    @Test
    fun `test fetch Outbound Trasnfer Orders with GRN`() = runBlocking {
        val order3DateRange = DateRange(
            fromDate = order3StartTime.toLocalDate(),
            toDate = order3EndTime.toLocalDate(),
        )

        val outBound = transferOrderRepository.fetchOutboundOrders(
            dcCodes = setOf(dcVE.dcCode),
            dateRange = order3DateRange
        )

        with(outBound.first()) {
            assertEquals("TO-$order3Number", transferOrderNumber)
            assertEquals(dcVE.dcCode, sourceDc)
            assertEquals(1, transferOrderSkus.size)
            assertEquals(sku3Id, transferOrderSkus.first().skuId)
            assertEquals(1, transferOrderSkus.first().deliveries.size)
            assertEquals(SkuQuantity.fromLong(100), transferOrderSkus.first().expectedQuantity)
        }
    }
}
