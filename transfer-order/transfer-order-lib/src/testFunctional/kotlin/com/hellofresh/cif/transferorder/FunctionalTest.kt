import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.transfer_order_lib.schema.Tables.DC_CONFIG
import com.hellofresh.cif.transfer_order_lib.schema.Tables.GOODS_RECEIVED_NOTE
import com.hellofresh.cif.transfer_order_lib.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.transfer_order_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDERS_GRN_VIEW
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER_SKUS
import com.hellofresh.cif.transfer_order_lib.schema.enums.TransferOrderStatus as TransferOrderStatusInDb
import com.hellofresh.cif.transfer_order_lib.schema.enums.Uom
import com.hellofresh.cif.transferorder.db.TransferOrderRepositoryImpl
import com.hellofresh.cif.transferorder.model.TransferOrderEntity
import com.hellofresh.cif.transferorder.model.TransferOrderSkuEntity
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {
    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(TRANSFER_ORDER_SKUS).execute()
        dsl.deleteFrom(TRANSFER_ORDER).execute()
        dsl.deleteFrom(GOODS_RECEIVED_NOTE).execute()
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
        dsl.deleteFrom(DC_CONFIG).execute()
        refreshTransferOrderView()
        refreshSkuSpecificationView()
    }

    fun insertTransferOrder(orders: List<TransferOrderEntity>) {
        orders.forEach {
            dsl.insertInto(
                TRANSFER_ORDER,
                TRANSFER_ORDER.TRANSFER_ORDER_NUMBER,
                TRANSFER_ORDER.SOURCE_DC,
                TRANSFER_ORDER.DESTINATION_DC,
                TRANSFER_ORDER.STATUS,
                TRANSFER_ORDER.WEEK,
                TRANSFER_ORDER.DELIVERY_START_TIME,
                TRANSFER_ORDER.DELIVERY_END_TIME,
                TRANSFER_ORDER.MARKET_CODE,
            )
                .values(
                    it.transferOrderNumber,
                    it.sourceDc,
                    it.destinationDc,
                    TransferOrderStatusInDb.STATE_RESERVED,
                    it.week,
                    it.deliveryStartTime,
                    it.deliveryEndTime,
                    it.marketCode,
                )
                .execute()
        }
    }

    fun insertTransferOrderDetails(transferOrderNumber: String, orders: List<TransferOrderSkuEntity>) {
        orders.forEach {
            dsl.insertInto(
                TRANSFER_ORDER_SKUS,
                TRANSFER_ORDER_SKUS.TRANSFER_ORDER_NUMBER,
                TRANSFER_ORDER_SKUS.SKU_ID,
                TRANSFER_ORDER_SKUS.SUPPLIER_ID,
                TRANSFER_ORDER_SKUS.UOM,
                TRANSFER_ORDER_SKUS.QUANTITY,
                TRANSFER_ORDER_SKUS.LOT_EXPIRATION_TIME,
            )
                .values(
                    transferOrderNumber,
                    it.skuId,
                    it.supplierId,
                    Uom.UOM_UNIT,
                    it.quantity.getValue(),
                    it.lotExpirationTime,
                )
                .execute()
        }
    }

    fun createTestOrder(
        orderNumber: String,
        sourceDc: String,
        destinationDc: String = "WW",
        skus: List<TransferOrderSkuEntity> = emptyList(),
        startTime: OffsetDateTime = OffsetDateTime.now(),
        endTime: OffsetDateTime = OffsetDateTime.now().plusDays(1),
        marketCode: String = "UK",
    ) = TransferOrderEntity(
        transferOrderNumber = "TO-$orderNumber",
        sourceDc = sourceDc,
        destinationDc = destinationDc,
        status = TransferOrderStatus.STATE_RESERVED,
        week = "2025-W25",
        deliveryStartTime = startTime,
        deliveryEndTime = endTime,
        marketCode = marketCode,
        skus = skus
    )

    fun createTestSku(transferOrderNumber: String, skuId: UUID = UUID.randomUUID(), quantity: Long = 5) = TransferOrderSkuEntity(
        transferOrderNumber = transferOrderNumber,
        skuId = skuId,
        supplierId = UUID.randomUUID(),
        quantity = SkuQuantity.fromLong(quantity, SkuUOM.UOM_UNIT),
        lotExpirationTime = OffsetDateTime.now().plusMonths(3)
    )

    fun createDC(dcCode: String) = getDcConfig(dcCode = dcCode)

    fun createSkuSpec(id: UUID, skuCode: String, skuName: String) = SkuSpecification(
        id = id,
        category = "PHF",
        skuCode = skuCode,
        name = skuName,
        coolingType = "test1",
        packaging = "test2",
        acceptableCodeLife = 0,
        market = "UK",
    )

    fun createGRN(dcCode: String, poNumber: String, skuId: UUID, quantity: BigDecimal, deliveryDate: OffsetDateTime = OffsetDateTime.now()) =
        GoodsReceivedNoteSku(
            dcCode = dcCode,
            poNumber = poNumber,
            poRef = poNumber,
            skuId = skuId,
            deliveryId = "1234",
            deliveryDate = deliveryDate,
            quantity = quantity,
            grnStatus = "CLOSED",
        )

    fun insertDC(dcs: List<DistributionCenterConfiguration>) {
        dcs.forEach { dc ->
            dsl.insertInto(
                DC_CONFIG,
                DC_CONFIG.DC_CODE,
                DC_CONFIG.MARKET,
                DC_CONFIG.PRODUCTION_START,
                DC_CONFIG.ZONE_ID,
                DC_CONFIG.ENABLED,
                DC_CONFIG.RECORD_TIMESTAMP,
                DC_CONFIG.HAS_CLEARDOWN,
            )
                .values(
                    dc.dcCode,
                    dc.market,
                    dc.productionStart.toString(),
                    dc.zoneId.toString(),
                    dc.enabled,
                    LocalDateTime.now(),
                    dc.hasCleardown,
                )
                .execute()
        }
    }

    fun insertSkuSpecification(skuCodes: List<SkuSpecification>) {
        skuCodes.forEach { skuItem ->
            dsl.insertInto(
                SKU_SPECIFICATION,
                SKU_SPECIFICATION.ID,
                SKU_SPECIFICATION.PARENT_ID,
                SKU_SPECIFICATION.CATEGORY,
                SKU_SPECIFICATION.CODE,
                SKU_SPECIFICATION.NAME,
                SKU_SPECIFICATION.COOLING_TYPE,
                SKU_SPECIFICATION.PACKAGING,
                SKU_SPECIFICATION.ACCEPTABLE_CODE_LIFE,
                SKU_SPECIFICATION.MARKET,
            )
                .values(
                    skuItem.id,
                    null,
                    skuItem.category,
                    skuItem.skuCode,
                    skuItem.name,
                    skuItem.coolingType,
                    skuItem.packaging,
                    skuItem.acceptableCodeLife,
                    skuItem.market,
                )
                .execute()
        }
    }

    fun insertGoodsReceivedNotes(goodsReceivedNotes: List<GoodsReceivedNoteSku>) {
        goodsReceivedNotes
            .forEach { grnRecord ->
                with(GOODS_RECEIVED_NOTE) {
                    dsl.insertInto(this).columns(
                        SKU_ID,
                        DC_CODE,
                        DELIVERY_ID,
                        DELIVERY_TIME,
                        QUANTITY,
                        DELIVERY_STATUS,
                        PO_REF,
                        PO_NUMBER,
                    ).values(
                        grnRecord.skuId,
                        grnRecord.dcCode,
                        grnRecord.deliveryId,
                        grnRecord.deliveryDate,
                        grnRecord.quantity,
                        grnRecord.grnStatus,
                        grnRecord.poRef,
                        grnRecord.poNumber,
                    ).execute()
                }
            }
    }

    fun refreshTransferOrderView() =
        dsl.query("refresh materialized view ${TRANSFER_ORDERS_GRN_VIEW.name}").execute()

    fun refreshSkuSpecificationView() =
        dsl.query("refresh materialized view ${SKU_SPECIFICATION_VIEW.name}").execute()

    companion object {
        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)
        lateinit var dsl: MetricsDSLContext
        lateinit var transferOrderRepository: TransferOrderRepositoryImpl
        lateinit var dcConfigService: DcConfigService

        @JvmStatic
        @BeforeAll
        fun init() {
            val config = DefaultConfiguration()
                .set(dataSource)
                .set(POSTGRES)
                .set(Executors.newSingleThreadExecutor())

            dsl = DSL.using(config).withMetrics(SimpleMeterRegistry())
            dcConfigService = DcConfigService(
                SimpleMeterRegistry(), repo = {
                    listOf(
                        getDcConfig("GR"),
                        getDcConfig("VE"),
                        getDcConfig("VB"),
                        getDcConfig("BX"),
                    )
                }
            )
            transferOrderRepository = TransferOrderRepositoryImpl(dsl, dcConfigService)
        }
    }
}

fun getDcConfig(dcCode: String) =
    DistributionCenterConfiguration(
        dcCode = dcCode,
        productionStart = LocalDate.now().dayOfWeek,
        cleardown = LocalDate.now().dayOfWeek,
        market = "UK",
        zoneId = ZoneOffset.UTC,
        enabled = true,
        wmsType = WmsSystem.WMS_SYSTEM_FCMS,
    )

data class SkuSpecification(
    val id: UUID,
    val category: String,
    val coolingType: String,
    val skuCode: String,
    val name: String,
    val acceptableCodeLife: Int,
    val packaging: String,
    val market: String,
    val uom: SkuUOM = SkuUOM.UOM_UNIT,
)

data class GoodsReceivedNoteSku(
    val dcCode: String,
    val poNumber: String,
    val poRef: String,
    val skuId: UUID,
    val deliveryId: String,
    val deliveryDate: OffsetDateTime,
    val quantity: BigDecimal,
    val grnStatus: String,
)
