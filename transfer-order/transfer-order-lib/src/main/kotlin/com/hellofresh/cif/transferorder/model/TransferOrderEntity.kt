package com.hellofresh.cif.transferorder.model

import com.hellofresh.cif.models.SkuQuantity
import java.time.OffsetDateTime
import java.util.UUID

data class TransferOrderEntity(
    val transferOrderNumber: String,
    val sourceDc: String,
    val destinationDc: String,
    val status: TransferOrderStatus,
    val week: String,
    val deliveryStartTime: OffsetDateTime,
    val deliveryEndTime: OffsetDateTime,
    val marketCode: String,
    val skus: List<TransferOrderSkuEntity>,
    val createdAt: OffsetDateTime = OffsetDateTime.now(),
    val updatedAt: OffsetDateTime = OffsetDateTime.now(),
)

data class TransferOrderSkuEntity(
    val transferOrderNumber: String,
    val skuId: UUID,
    val supplierId: UUID,
    val quantity: SkuQuantity,
    val lotExpirationTime: OffsetDateTime?,
    val createdAt: OffsetDateTime = OffsetDateTime.now(),
    val updatedAt: OffsetDateTime = OffsetDateTime.now(),
)

enum class TransferOrderStatus {
    STATE_RESERVED, // the goods have been set aside by the sending entity
    STATE_ORDERED, // the order was issued to other systems to be actioned, but has not yet been approved or rejected
    STATE_DELIVERED, //  received, the transfer is complete
    STATE_CANCELLED,
    STATE_DELETED,
    UNRECOGNIZED;

    fun isUsable() = this in setOf(
        STATE_ORDERED,
        STATE_RESERVED,
        STATE_DELIVERED,
    )
}
