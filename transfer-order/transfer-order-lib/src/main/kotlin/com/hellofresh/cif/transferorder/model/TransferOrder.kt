package com.hellofresh.cif.transferorder.model

import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.util.UUID

data class TransferOrder(
    val transferOrderNumber: String,
    val sourceDc: String,
    val destinationDc: String,
    val week: String,
    val marketCode: String,
    val status: TransferOrderStatus,
    val expectedDeliveryTimeslot: ToTimeRange? = null,
    val transferOrderSkus: List<TransferOrderSku>,
) {

    init {
        if (expectedDeliveryTimeslot == null && transferOrderSkus.isEmpty()) {
            throw IllegalArgumentException(
                "transfer order skus could not be empty, required at " +
                    "least 1 transfer order sku, transferOrderNumber = $transferOrderNumber," +
                    "sourceDc = $sourceDc, destinationDc = $destinationDc",
            )
        }
    }
}

data class TransferOrderSku(
    val toNumber: String,
    val skuId: UUID,
    val supplierId: UUID, // original supplier id
    val supplierName: String?, // original supplier name
    val expectedQuantity: SkuQuantity,
    val expireDate: LocalDate? = null,
    val deliveries: List<DeliveryInfo>,
)

data class DeliveryInfo(
    val id: String,
    val deliveryTime: LocalDateTime,
    val state: DeliveryInfoStatus,
    val quantity: SkuQuantity,
    val expiryDate: LocalDate? = null
) {
    companion object
}

enum class DeliveryInfoStatus {
    OPEN, CLOSED
}

data class Supplier(
    val id: UUID,
    val name: String,
)

data class ToTimeRange(
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime
) {
    val expectedDeliveryTime: ZonedDateTime
        get() = startTime
    val expectedDeliveryDate = startTime.toLocalDate()
}
