package com.hellofresh.cif.transferorder.db

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDERS_GRN_VIEW
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER_SKUS
import com.hellofresh.cif.transferorder.TransferOrderRecordBuilder
import com.hellofresh.cif.transferorder.TransferOrderViewBuilder
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderEntity
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Condition
import org.jooq.impl.DSL.noCondition

class TransferOrderRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
) : TransferOrderRepository {
    override suspend fun findAll(): List<TransferOrderEntity> =
        metricsDSLContext.withTagName("find-all-transfer-orders")
            .select()
            .from(TRANSFER_ORDER)
            .leftJoin(TRANSFER_ORDER_SKUS)
            .on(TRANSFER_ORDER.TRANSFER_ORDER_NUMBER.eq(TRANSFER_ORDER_SKUS.TRANSFER_ORDER_NUMBER))
            .fetchAsync()
            .thenApply { result ->
                TransferOrderRecordBuilder.buildTransferOrders(result)
            }
            .await()

    override suspend fun findByMarketAndSourceDC(
        marketCode: String,
        sourceDc: String
    ): List<TransferOrderEntity> = metricsDSLContext.withTagName("find-transfer-orders-by-market")
        .select()
        .from(TRANSFER_ORDER)
        .leftJoin(TRANSFER_ORDER_SKUS)
        .on(TRANSFER_ORDER.TRANSFER_ORDER_NUMBER.eq(TRANSFER_ORDER_SKUS.TRANSFER_ORDER_NUMBER))
        .where(
            TRANSFER_ORDER.MARKET_CODE.eq(marketCode)
                .and(TRANSFER_ORDER.SOURCE_DC.eq(sourceDc))
        )
        .fetchAsync()
        .thenApply { result ->
            TransferOrderRecordBuilder.buildTransferOrders(result)
        }
        .await()

    override suspend fun fetchInboundOrders(dcCodes: Set<String>, dateRange: DateRange): List<TransferOrder> = run {
        logger.info("Fetching Transfer Order data for dcs: ${dcCodes.joinToString { ", " }} ")

        metricsDSLContext
            .withTagName("fetch-inbounds-transfer-orders-by-dcs")
            .selectTransferOrdersGrnView()
            .from(TRANSFER_ORDERS_GRN_VIEW)
            .where(
                TRANSFER_ORDERS_GRN_VIEW.TO_LOCAL_EXPECTED_DELIVERY_START_TIME.between(
                    atStartOfDayInLocalTime(dateRange.fromDate),
                    atEndOfDayInLocalTime(dateRange.toDate),
                ).or(
                    TRANSFER_ORDERS_GRN_VIEW.GRN_LOCAL_DELIVERY_TIME.between(
                        atStartOfDayInLocalTime(dateRange.fromDate),
                        atEndOfDayInLocalTime(dateRange.toDate),
                    ),
                ),
            )
            .and(
                TRANSFER_ORDERS_GRN_VIEW.TO_DESTINATION_DC.`in`(dcCodes)
                    .or(TRANSFER_ORDERS_GRN_VIEW.GRN_DC_CODE.`in`(dcCodes))
            )
            .fetchAsync()
            .thenApply { result ->
                TransferOrderViewBuilder.build(result, dcConfigService)
            }
            .await()
    }

    override suspend fun fetchOutboundOrders(dcCodes: Set<String>, dateRange: DateRange): List<TransferOrder> = run {
        logger.info(
            "Fetching Transfer Order Outbound data for dcs: ${dcCodes.joinToString { ", " }}"
        )

        metricsDSLContext
            .withTagName("fetch-transfer-orders-by-dcs-for-outbound")
            .selectTransferOrdersGrnView()
            .from(TRANSFER_ORDERS_GRN_VIEW)
            .where(
                TRANSFER_ORDERS_GRN_VIEW.TO_LOCAL_EXPECTED_DELIVERY_START_TIME.between(
                    atStartOfDayInLocalTime(dateRange.fromDate),
                    atEndOfDayInLocalTime(dateRange.toDate),
                ).or(
                    TRANSFER_ORDERS_GRN_VIEW.GRN_LOCAL_DELIVERY_TIME.between(
                        atStartOfDayInLocalTime(dateRange.fromDate),
                        atEndOfDayInLocalTime(dateRange.toDate),
                    ),
                ),
            )
            .and(
                TRANSFER_ORDERS_GRN_VIEW.TO_SOURCE_DC.`in`(dcCodes)
                    .or(TRANSFER_ORDERS_GRN_VIEW.GRN_DC_CODE.`in`(dcCodes))
            )
            .fetchAsync()
            .thenApply { result ->

                TransferOrderViewBuilder.build(result, dcConfigService)
            }
            .await()
    }

    override suspend fun findTransferOrders(
        skuId: UUID,
        dcCodes: Set<String>,
        dateRanges: List<DateRange>
    ): List<TransferOrder> = metricsDSLContext.withTagName("transfer-order-by-date-range")
        .selectTransferOrdersGrnView()
        .from(TRANSFER_ORDERS_GRN_VIEW)
        .where(
            buildDateRangeCondition(dateRanges)
                .and(
                    TRANSFER_ORDERS_GRN_VIEW.TO_SKU_ID.eq(skuId)
                        .or(TRANSFER_ORDERS_GRN_VIEW.GRN_SKU_ID.eq(skuId))
                )
                .and(
                    TRANSFER_ORDERS_GRN_VIEW.TO_SOURCE_DC.`in`(dcCodes)
                        .or(TRANSFER_ORDERS_GRN_VIEW.TO_DESTINATION_DC.`in`(dcCodes))
                        .or(TRANSFER_ORDERS_GRN_VIEW.GRN_DC_CODE.`in`(dcCodes))
                )
        )
        .fetchAsync()
        .thenApply { result ->
            TransferOrderViewBuilder.build(result, dcConfigService)
        }
        .await()

    private fun MetricsDSLContext.selectTransferOrdersGrnView() = select(
        TRANSFER_ORDERS_GRN_VIEW.TO_NUMBER,
        TRANSFER_ORDERS_GRN_VIEW.TO_STATUS,
        TRANSFER_ORDERS_GRN_VIEW.TO_SOURCE_DC,
        TRANSFER_ORDERS_GRN_VIEW.TO_DESTINATION_DC,
        TRANSFER_ORDERS_GRN_VIEW.TO_SKU_ID,
        TRANSFER_ORDERS_GRN_VIEW.TO_SUPPLIER_ID,
        TRANSFER_ORDERS_GRN_VIEW.TO_SUPPLIER_NAME,
        TRANSFER_ORDERS_GRN_VIEW.TO_QUANTITY,
        TRANSFER_ORDERS_GRN_VIEW.TO_UOM,
        TRANSFER_ORDERS_GRN_VIEW.TO_WEEK,
        TRANSFER_ORDERS_GRN_VIEW.TO_MARKET_CODE,
        TRANSFER_ORDERS_GRN_VIEW.TO_DELIVERY_START_TIME,
        TRANSFER_ORDERS_GRN_VIEW.TO_DELIVERY_END_TIME,
        TRANSFER_ORDERS_GRN_VIEW.TO_LOCAL_EXPECTED_DELIVERY_START_TIME,
        TRANSFER_ORDERS_GRN_VIEW.TO_LOCAL_EXPECTED_DELIVERY_END_TIME,
        TRANSFER_ORDERS_GRN_VIEW.GRN_DC_CODE,
        TRANSFER_ORDERS_GRN_VIEW.GRN_DELIVERY_ID,
        TRANSFER_ORDERS_GRN_VIEW.GRN_EXPIRE_DATE,
        TRANSFER_ORDERS_GRN_VIEW.GRN_SKU_ID,
        TRANSFER_ORDERS_GRN_VIEW.GRN_QUANTITY,
        TRANSFER_ORDERS_GRN_VIEW.GRN_UOM,
        TRANSFER_ORDERS_GRN_VIEW.GRN_LOCAL_DELIVERY_TIME,
        TRANSFER_ORDERS_GRN_VIEW.GRN_DELIVERY_TIME,
        TRANSFER_ORDERS_GRN_VIEW.GRN_DELIVERY_STATUS,
    )

    private fun atStartOfDayInLocalTime(date: LocalDate) =
        date.atStartOfDay()

    private fun atEndOfDayInLocalTime(date: LocalDate) =
        date.atTime(LocalTime.MAX)

    private fun buildDateRangeCondition(dateRanges: List<DateRange>): Condition =
        dateRanges
            .map { range ->
                val from = atStartOfDayInLocalTime(range.fromDate)
                val to = atEndOfDayInLocalTime(range.toDate)
                TRANSFER_ORDERS_GRN_VIEW.TO_LOCAL_EXPECTED_DELIVERY_START_TIME.between(from, to)
                    .or(TRANSFER_ORDERS_GRN_VIEW.GRN_LOCAL_DELIVERY_TIME.between(from, to))
            }
            .reduceOrNull { acc, condition -> acc.or(condition) }
            ?: noCondition()

    companion object : Logging
}
