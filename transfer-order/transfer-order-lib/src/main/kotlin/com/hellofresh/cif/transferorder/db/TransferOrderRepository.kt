package com.hellofresh.cif.transferorder.db

import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderEntity
import java.util.UUID

interface TransferOrderRepository {
    suspend fun findAll(): List<TransferOrderEntity>

    suspend fun findByMarketAndSourceDC(
        marketCode: String,
        sourceDc: String
    ): List<TransferOrderEntity>

    suspend fun fetchInboundOrders(dcCodes: Set<String>, dateRange: DateRange): List<TransferOrder>
    suspend fun fetchOutboundOrders(dcCodes: Set<String>, dateRange: DateRange): List<TransferOrder>

    suspend fun findTransferOrders(
        skuId: UUID,
        dcCodes: Set<String>,
        dateRanges: List<DateRange>
    ): List<TransferOrder>
}
