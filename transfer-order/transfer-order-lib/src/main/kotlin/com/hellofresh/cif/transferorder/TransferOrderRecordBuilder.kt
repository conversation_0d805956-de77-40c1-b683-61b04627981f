package com.hellofresh.cif.transferorder

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER_SKUS
import com.hellofresh.cif.transfer_order_lib.schema.enums.TransferOrderStatus as TransferOrderStatusInDb
import com.hellofresh.cif.transfer_order_lib.schema.enums.Uom
import com.hellofresh.cif.transferorder.model.TransferOrderEntity
import com.hellofresh.cif.transferorder.model.TransferOrderSkuEntity
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import org.jooq.Record
import org.jooq.Result

object TransferOrderRecordBuilder {
    fun buildTransferOrders(records: Result<Record>): List<TransferOrderEntity> = records
        .groupBy { it[TRANSFER_ORDER.TRANSFER_ORDER_NUMBER] }
        .map { (_, group) ->
            val orderRecord = group.first()
            val skus = group
                .mapNotNull { record ->
                    record[TRANSFER_ORDER_SKUS.SKU_ID]?.let { skuId ->
                        TransferOrderSkuEntity(
                            transferOrderNumber = orderRecord[TRANSFER_ORDER.TRANSFER_ORDER_NUMBER],
                            skuId = skuId,
                            supplierId = record[TRANSFER_ORDER_SKUS.SUPPLIER_ID],
                            quantity = SkuQuantity.fromBigDecimal(
                                record[TRANSFER_ORDER_SKUS.QUANTITY],
                                mapUomToSkuUOM(record[TRANSFER_ORDER_SKUS.UOM])
                            ),
                            lotExpirationTime = record[TRANSFER_ORDER_SKUS.LOT_EXPIRATION_TIME],
                            createdAt = record[TRANSFER_ORDER_SKUS.CREATED_AT],
                            updatedAt = record[TRANSFER_ORDER_SKUS.UPDATED_AT],
                        )
                    }
                }

            TransferOrderEntity(
                transferOrderNumber = orderRecord[TRANSFER_ORDER.TRANSFER_ORDER_NUMBER],
                sourceDc = orderRecord[TRANSFER_ORDER.SOURCE_DC],
                destinationDc = orderRecord[TRANSFER_ORDER.DESTINATION_DC],
                status = mapTransferOrderStatusToStatus(orderRecord[TRANSFER_ORDER.STATUS]),
                week = orderRecord[TRANSFER_ORDER.WEEK],
                deliveryStartTime = orderRecord[TRANSFER_ORDER.DELIVERY_START_TIME],
                deliveryEndTime = orderRecord[TRANSFER_ORDER.DELIVERY_END_TIME],
                marketCode = orderRecord[TRANSFER_ORDER.MARKET_CODE],
                skus = skus,
                createdAt = orderRecord[TRANSFER_ORDER.CREATED_AT],
                updatedAt = orderRecord[TRANSFER_ORDER.UPDATED_AT],
            )
        }
}

fun mapUomToSkuUOM(uom: Uom): SkuUOM =
    when (uom) {
        Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
        Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
        Uom.UOM_KG -> SkuUOM.UOM_KG
        Uom.UOM_LBS -> SkuUOM.UOM_LBS
        Uom.UOM_GAL -> SkuUOM.UOM_GAL
        Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
        Uom.UOM_OZ -> SkuUOM.UOM_OZ
        Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
    }

fun mapTransferOrderStatusToStatus(status: TransferOrderStatusInDb): TransferOrderStatus = when (status) {
    TransferOrderStatusInDb.STATE_ORDERED -> TransferOrderStatus.STATE_ORDERED
    TransferOrderStatusInDb.STATE_RESERVED -> TransferOrderStatus.STATE_RESERVED
    TransferOrderStatusInDb.STATE_DELIVERED -> TransferOrderStatus.STATE_DELIVERED
    TransferOrderStatusInDb.STATE_CANCELLED -> TransferOrderStatus.STATE_CANCELLED
    TransferOrderStatusInDb.STATE_DELETED -> TransferOrderStatus.STATE_DELETED
}
