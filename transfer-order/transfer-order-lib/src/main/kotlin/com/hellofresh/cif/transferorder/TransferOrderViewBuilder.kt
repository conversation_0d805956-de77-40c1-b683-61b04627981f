package com.hellofresh.cif.transferorder

import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDERS_GRN_VIEW
import com.hellofresh.cif.transferorder.model.DeliveryInfo
import com.hellofresh.cif.transferorder.model.DeliveryInfoStatus
import com.hellofresh.cif.transferorder.model.ToTimeRange
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderSku
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import java.time.ZoneOffset
import org.jooq.Record
import org.jooq.Result

object TransferOrderViewBuilder {
    fun build(
        records: Result<Record>,
        dcConfigService: DcConfigService,
    ): List<TransferOrder> {
        val result = records
            .groupBy { it[TRANSFER_ORDERS_GRN_VIEW.TO_NUMBER] }
            .mapNotNull { (_, ordersByToNumber) ->
                if (ordersByToNumber.isEmpty()) {
                    return@mapNotNull null
                }
                val toRecord = ordersByToNumber.first()

                val transferOrderSkus = ordersByToNumber
                    .mapNotNull { record ->
                        record[TRANSFER_ORDERS_GRN_VIEW.TO_SKU_ID]?.let { skuId ->
                            createSkuWithDelivery(record)
                        } // RECORD END
                    } // SKUS END

                val destinationDc = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_DESTINATION_DC]
                check(dcConfigService.dcConfigurations[destinationDc] != null) {
                    "DC configuration not found for Destination DC code: $destinationDc"
                }
                val localDcZoneId = dcConfigService.dcConfigurations[destinationDc]?.zoneId ?: ZoneOffset.UTC

                with(toRecord) {
                    val timeSlot = ToTimeRange(
                        startTime = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_LOCAL_EXPECTED_DELIVERY_START_TIME].atZone(
                            localDcZoneId
                        ),
                        endTime = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_LOCAL_EXPECTED_DELIVERY_END_TIME].atZone(
                            localDcZoneId
                        ),
                    )

                    TransferOrder(
                        transferOrderNumber = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_NUMBER],
                        sourceDc = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_SOURCE_DC],
                        destinationDc = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_DESTINATION_DC],
                        week = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_WEEK],
                        marketCode = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_MARKET_CODE],
                        status = toRecord[TRANSFER_ORDERS_GRN_VIEW.TO_STATUS]?.let { status ->
                            TransferOrderStatus.valueOf(status.name)
                        } ?: TransferOrderStatus.UNRECOGNIZED,
                        expectedDeliveryTimeslot = timeSlot,
                        transferOrderSkus = transferOrderSkus,
                    )
                }
            }

        return result
    }

    private fun createSkuWithDelivery(
        record: Record,
    ): TransferOrderSku {
        val deliveries = if (record[TRANSFER_ORDERS_GRN_VIEW.GRN_SKU_ID] != null) {
            listOf(
                DeliveryInfo(
                    id = record[TRANSFER_ORDERS_GRN_VIEW.GRN_DELIVERY_ID],
                    deliveryTime = record[TRANSFER_ORDERS_GRN_VIEW.GRN_LOCAL_DELIVERY_TIME],
                    state = DeliveryInfoStatus.valueOf(record[TRANSFER_ORDERS_GRN_VIEW.GRN_DELIVERY_STATUS]),
                    quantity = SkuQuantity.fromBigDecimal(
                        value = record[TRANSFER_ORDERS_GRN_VIEW.GRN_QUANTITY],
                        unitOfMeasure = mapUomToSkuUOM(record[TRANSFER_ORDERS_GRN_VIEW.GRN_UOM])
                    ),
                    expiryDate = record[TRANSFER_ORDERS_GRN_VIEW.GRN_EXPIRE_DATE]
                )
            )
        } else {
            emptyList()
        }

        return TransferOrderSku(
            toNumber = record[TRANSFER_ORDERS_GRN_VIEW.TO_NUMBER],
            skuId = record[TRANSFER_ORDERS_GRN_VIEW.TO_SKU_ID],
            supplierId = record[TRANSFER_ORDERS_GRN_VIEW.TO_SUPPLIER_ID],
            supplierName = record[TRANSFER_ORDERS_GRN_VIEW.TO_SUPPLIER_NAME],
            expectedQuantity = SkuQuantity.fromBigDecimal(
                value = record[TRANSFER_ORDERS_GRN_VIEW.TO_QUANTITY],
                unitOfMeasure = mapUomToSkuUOM(record[TRANSFER_ORDERS_GRN_VIEW.TO_UOM])
            ),
            deliveries = deliveries
        )
    }
}
