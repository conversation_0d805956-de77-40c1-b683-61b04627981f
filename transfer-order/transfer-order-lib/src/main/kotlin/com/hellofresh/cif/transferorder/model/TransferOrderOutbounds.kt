package com.hellofresh.cif.transferorder.model

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuQuantity.Companion.ZERO
import com.hellofresh.cif.models.sum
import com.hellofresh.cif.models.sumOf
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.UUID

data class TransferOrderOutbounds(val tos: List<TransferOrder>) {
    private val transferOrdersBySkuDc: Map<SkuToKey, List<SkuToValue>> by lazy {
        tos.asSequence()
            .filter { to -> to.status.isUsable() }
            .flatMap { to ->
                to.transferOrderSkus.map { skuTo ->
                    SkuToKey(skuTo.skuId, to.sourceDc) to
                        SkuToValue(to, skuTo)
                }
            }
            .groupBy({ it.first }, { it.second })
    }

    fun outbounds(skuId: UUID, dcCode: String, date: LocalDate): Outbounds =
        outbounds(skuId, dcCode, listOf(date))

    fun outbounds(skuId: UUID, dcCode: String, dates: List<LocalDate>): Outbounds {
        val skuTos = transferOrdersBySkuDc[SkuToKey(skuId, dcCode)] ?: emptyList()

        return skuTos
            .map { (to, transferOrderSkus) ->
                var expectedTransferOrderSku: TransferOrderSku? = null
                var remainingExpectedQuantity: SkuQuantity? = null

                val deliveries = transferOrderSkus.deliveries.filter {
                    it.deliveryTime.toLocalDate() in dates
                }.let { deliveryList ->
                    deliveryList.map {
                        DeliveryToOutbound(
                            sourceTo = to,
                            deliveryTime = it.deliveryTime,
                            actualQuantity = it.quantity,
                            actualExpiryDate = it.expiryDate
                        )
                    }
                }

                if (to.expectedDeliveryTimeslot != null && to.expectedDeliveryTimeslot.expectedDeliveryDate in dates) {
                    expectedTransferOrderSku = transferOrderSkus

                    if (transferOrderSkus.deliveries.hasPendingDeliveries() &&
                        isToStillExpected(to.expectedDeliveryTimeslot.endTime)
                    ) {
                        remainingExpectedQuantity = SkuQuantity.max(
                            valueA = ZERO,
                            valueB = (transferOrderSkus.expectedQuantity ?: ZERO)
                                .minus(transferOrderSkus.deliveries.sumOf { it.quantity }),
                        )
                    }
                }

                Outbounds(
                    tos = if (expectedTransferOrderSku != null || deliveries.isNotEmpty()) {
                        listOf(
                            ToOutbounds(
                                sourceTo = to,
                                sourceToSku = transferOrderSkus,
                                expectedTransferOrderSku = expectedTransferOrderSku,
                                remainingExpectedQuantity = remainingExpectedQuantity,
                                deliveries = deliveries
                            )
                        )
                    } else {
                        emptyList()
                    },
                )
            }.fold(
                Outbounds(listOf()),
            ) { acc, cur ->
                Outbounds(acc.tos + cur.tos)
            }
    }
    private fun List<DeliveryInfo>.hasPendingDeliveries() =
        this.isEmpty() || this.any { it.state == DeliveryInfoStatus.OPEN }

    private fun isToStillExpected(startTime: ZonedDateTime) =
        LocalDateTime.now(startTime.zone).isBefore(
            startTime.toLocalDateTime().with(EXPECTED_LOCAL_TIME_LIMIT),
        )

    private data class SkuToKey(val skuId: UUID, val dcCode: String)
    private data class SkuToValue(val transferOrder: TransferOrder, val skuTo: TransferOrderSku)

    companion object {
        val EXPECTED_LOCAL_TIME_LIMIT: LocalTime = LocalTime.of(23, 59, 59)
    }
}

data class Outbounds(val tos: List<ToOutbounds>) {

    val expectedToNumbers: Set<String>
        get() = tos.mapNotNull { it.expectedToNumber }.toSet()

    val actualToNumbers: Set<String>
        get() = tos.flatMap { it.deliveries.map { delivery -> delivery.sourceTo.transferOrderNumber } }.toSet()

    val actualQuantity: SkuQuantity
        get() = tos.sumOf { it.actualQuantity }

    val remainingExpectedQuantity: SkuQuantity?
        get() = tos.sumOf { it.remainingExpectedQuantity ?: SkuQuantity.ZERO }

    val expectedQuantity: SkuQuantity
        get() = tos.mapNotNull { it.expectedQuantity }.sum()
}

data class ToOutbounds(
    val sourceTo: TransferOrder,
    val sourceToSku: TransferOrderSku,
    val expectedTransferOrderSku: TransferOrderSku?,
    val remainingExpectedQuantity: SkuQuantity?,
    val deliveries: List<DeliveryToOutbound>,
) {

    val expectedToNumber: String?
        get() = expectedTransferOrderSku?.let { sourceTo.transferOrderNumber }

    val expectedExpiryDate: LocalDate?
        get() = expectedTransferOrderSku?.expireDate

    val actualQuantity: SkuQuantity = deliveries.sumOf { it.actualQuantity }

    val expectedQuantity: SkuQuantity?
        get() = expectedTransferOrderSku?.expectedQuantity

    companion object
}

data class DeliveryToOutbound(
    val sourceTo: TransferOrder,
    val deliveryTime: LocalDateTime,
    val actualQuantity: SkuQuantity,
    val actualExpiryDate: LocalDate?
) {
    companion object
}
