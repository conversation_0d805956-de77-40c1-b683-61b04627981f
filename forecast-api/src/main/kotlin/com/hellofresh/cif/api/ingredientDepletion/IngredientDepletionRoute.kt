package com.hellofresh.cif.api.ingredientDepletion

import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionRequest
import com.hellofresh.cif.api.error.handleResponseError
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import kotlinx.coroutines.withTimeout
import java.time.Duration

/**
 * Routing configuration for ingredient depletion view API
 */
fun Routing.ingredientDepletion(
    ingredientDepletionService: IngredientDepletionService,
    timeoutInMillis: Long
) = authenticate {
    route("/ingredientDepletion") {
        get("") {
            handleIngredientDepletionRequest(this.call, timeoutInMillis) { request ->
                ingredientDepletionService.getIngredientDepletionView(request)
            }
        }
    }
}

private suspend fun handleIngredientDepletionRequest(
    call: ApplicationCall,
    timeoutInMillis: Long,
    fetch: suspend (request: IngredientDepletionRequest) -> Any
) {
    withTimeout(timeoutInMillis) {
        runCatching {
            val request = IngredientDepletionRequest.from(call.parameters)

            if (request.dcCodes.isEmpty()) {
                call.respond(
                    HttpStatusCode.BadRequest,
                    mapOf("error" to "dcCode parameter is required"),
                )
                return@runCatching
            }

            fetch(request)
        }.onSuccess {
            call.respond(HttpStatusCode.OK, it)
        }.onFailure {
            call.handleResponseError(it)
        }
    }
}

/**
 * Module function to configure the ingredient depletion routing
 */
fun ingredientDepletionModule(
    ingredientDepletionService: IngredientDepletionService,
    timeout: Duration
): Application.() -> Unit = {
    routing {
        ingredientDepletion(
            ingredientDepletionService,
            timeout.toMillis(),
        )
    }
}
