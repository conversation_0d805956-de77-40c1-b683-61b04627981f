package com.hellofresh.cif.api.ingredientDepletion

import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionRequest
import com.hellofresh.cif.api.ingredientDepletion.model.IngredientSummary

/**
 * Repository interface for fetching ingredient depletion data
 */
interface IngredientDepletionRepository {

    /**
     * Fetches ingredient summary data based on the provided request parameters
     *
     * @param request The request parameters including DC codes
     * @return List of ingredient summaries
     */
    suspend fun fetchIngredientSummary(request: IngredientDepletionRequest): List<IngredientSummary>
}
