package com.hellofresh.cif.api.ingredientDepletion

import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionRequest
import com.hellofresh.cif.api.ingredientDepletion.model.IngredientSummary

/**
 * Repository interface for fetching ingredient depletion data
 */
interface IngredientDepletionRepository {
    
    /**
     * Fetches ingredient summary data based on the provided request parameters
     * 
     * @param request The request parameters including DC codes, pagination info
     * @return Pair of (list of ingredient summaries, total count)
     */
    suspend fun fetchIngredientSummary(request: IngredientDepletionRequest): Pair<List<IngredientSummary>, Long>
}
