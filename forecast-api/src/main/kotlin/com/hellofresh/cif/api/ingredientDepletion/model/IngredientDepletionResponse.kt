package com.hellofresh.cif.api.ingredientDepletion.model

import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.models.SkuUOM
import io.ktor.http.Parameters

/**
 * Response model for the ingredient depletion view API
 */
data class IngredientDepletionResponse(
    val ingredientSummary: List<IngredientSummary>
)

/**
 * Ingredient summary containing the core SKU information grouped together
 */
data class IngredientSummary(
    val site: String,           // Distribution Center Code (DC Code)
    val brand: String,          // Brand name
    val skuCode: String,        // SKU Code
    val skuName: String,        // SKU Name
    val category: String,       // SKU Category
    val uom: SkuUOM            // Unit of Measure
)

/**
 * Request parameters for ingredient depletion view
 */
data class IngredientDepletionRequest(
    val dcCodes: Set<String>,
    val page: Int = 0,
    val skuCount: Int = 6
) {
    companion object {
        fun from(parameters: Parameters): IngredientDepletionRequest {
            val dcCodes = parameters.getAllOrDefault("dcCode").flatMap { it.split(",") }.toSet()
            val page = parameters.get("page")?.toIntOrNull() ?: 0
            val skuCount = parameters.get("skuCount")?.toIntOrNull() ?: 6

            return IngredientDepletionRequest(
                dcCodes = dcCodes,
                page = page,
                skuCount = skuCount
            )
        }
    }
}
