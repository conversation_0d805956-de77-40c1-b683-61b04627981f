package com.hellofresh.cif.api.ingredientDepletion.model

import com.hellofresh.cif.models.SkuUOM

/**
 * Response model for the ingredient depletion view API
 */
data class IngredientDepletionResponse(
    val ingredientSummary: List<IngredientSummary>,
    val page: Int,
    val totalPages: Int,
    val totalCount: Long
)

/**
 * Ingredient summary containing the core SKU information grouped together
 */
data class IngredientSummary(
    val site: String,           // Distribution Center Code (DC Code)
    val brand: String,          // Brand name
    val skuCode: String,        // SKU Code
    val skuName: String,        // SKU Name
    val category: String,       // SKU Category
    val uom: SkuUOM            // Unit of Measure
)

/**
 * Request parameters for ingredient depletion view
 */
data class IngredientDepletionRequest(
    val dcCodes: Set<String>,
    val page: Int = 0,
    val skuCount: Int = 6
) {
    companion object {
        fun from(parameters: Map<String, List<String>>): IngredientDepletionRequest {
            val dcCodes = parameters["dcCode"]?.flatMap { it.split(",") }?.toSet() ?: emptySet()
            val page = parameters["page"]?.firstOrNull()?.toIntOrNull() ?: 0
            val skuCount = parameters["skuCount"]?.firstOrNull()?.toIntOrNull() ?: 6
            
            return IngredientDepletionRequest(
                dcCodes = dcCodes,
                page = page,
                skuCount = skuCount
            )
        }
    }
}
