package com.hellofresh.cif.api.ingredientDepletion.model

import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.models.SkuUOM
import io.ktor.http.Parameters


data class IngredientDepletionResponse(
    val ingredientSummary: List<IngredientSummary>
)

data class IngredientSummary(
    val site: String,
    val brand: String,
    val skuCode: String,
    val skuName: String,
    val category: String,
    val uom: SkuUOM
)

data class IngredientDepletionRequest(
    val dcCodes: Set<String>,
    val brand: String,
    val weeks: List<String> = emptyList(),
) {
    companion object {
        fun from(parameters: Parameters): IngredientDepletionRequest {
            val dcCodes = parameters.getAllOrDefault("dcCode").flatMap { it.split(",") }.toSet()
            val brand = parameters["brand"].orEmpty().trim()
            val weeks = parameters.getAllOrDefault("weeks").flatMap { it.split(",") }
            return IngredientDepletionRequest(
                dcCodes = dcCodes,
                brand = brand,
                weeks = weeks.ifEmpty { emptyList() }
            )
        }
    }
}
