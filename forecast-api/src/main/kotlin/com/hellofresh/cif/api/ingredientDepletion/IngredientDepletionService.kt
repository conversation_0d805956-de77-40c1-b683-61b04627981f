package com.hellofresh.cif.api.ingredientDepletion

import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionRequest
import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionResponse
import com.hellofresh.cif.distributionCenterLib.DcConfigService

class IngredientDepletionService(
    private val repository: IngredientDepletionRepository,
    private val dcConfigService: DcConfigService
) {

    suspend fun getIngredientDepletionView(request: IngredientDepletionRequest): IngredientDepletionResponse {
        // Get markets for the requested DC codes
        val markets = getMarketsForDcCodes(request.dcCodes)

        if (markets.isEmpty()) {
            return IngredientDepletionResponse(ingredientSummary = emptyList())
        }

        // Create a new request with markets instead of DC codes for the repository
        val marketRequest = IngredientDepletionRequest(dcCodes = markets)
        val ingredientSummary = repository.fetchIngredientSummary(marketRequest)

        return IngredientDepletionResponse(
            ingredientSummary = ingredientSummary
        )
    }

    private fun getMarketsForDcCodes(dcCodes: Set<String>): Set<String> {
        return dcCodes.mapNotNull { dcCode ->
            dcConfigService.dcConfigurations[dcCode]?.market
        }.toSet()
    }
}
