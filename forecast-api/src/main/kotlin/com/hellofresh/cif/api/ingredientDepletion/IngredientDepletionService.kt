package com.hellofresh.cif.api.ingredientDepletion

import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionRequest
import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionResponse

/**
 * Service for handling ingredient depletion view business logic
 */
class IngredientDepletionService(
    private val repository: IngredientDepletionRepository
) {

    /**
     * Fetches ingredient depletion data based on the request parameters
     *
     * @param request The request parameters including DC codes
     * @return IngredientDepletionResponse with all data
     */
    suspend fun getIngredientDepletionView(request: IngredientDepletionRequest): IngredientDepletionResponse {
        val ingredientSummary = repository.fetchIngredientSummary(request)

        return IngredientDepletionResponse(
            ingredientSummary = ingredientSummary
        )
    }
}
