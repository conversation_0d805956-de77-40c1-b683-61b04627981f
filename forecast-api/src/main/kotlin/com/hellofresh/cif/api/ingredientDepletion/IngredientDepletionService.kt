package com.hellofresh.cif.api.ingredientDepletion

import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionRequest
import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionResponse
import kotlin.math.ceil

/**
 * Service for handling ingredient depletion view business logic
 */
class IngredientDepletionService(
    private val repository: IngredientDepletionRepository
) {
    
    /**
     * Fetches ingredient depletion data based on the request parameters
     * 
     * @param request The request parameters including DC codes, pagination info
     * @return IngredientDepletionResponse with paginated data
     */
    suspend fun getIngredientDepletionView(request: IngredientDepletionRequest): IngredientDepletionResponse {
        val (ingredientSummary, totalCount) = repository.fetchIngredientSummary(request)
        
        val totalPages = if (request.skuCount > 0) {
            ceil(totalCount.toDouble() / request.skuCount).toInt()
        } else {
            1
        }
        
        return IngredientDepletionResponse(
            ingredientSummary = ingredientSummary,
            page = request.page,
            totalPages = totalPages,
            totalCount = totalCount
        )
    }
}
