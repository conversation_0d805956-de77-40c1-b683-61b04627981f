package com.hellofresh.cif.api.ingredientDepletion

import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionRequest
import com.hellofresh.cif.api.ingredientDepletion.model.IngredientSummary
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_inputs_lib.schema.enums.Uom
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.lower

/**
 * Repository implementation for fetching ingredient depletion data
 */
class IngredientDepletionRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService
) : IngredientDepletionRepository {

    private val fetchIngredientSummaryTag = "fetch-ingredient-summary"
    private val fetchIngredientSummaryCountTag = "fetch-ingredient-summary-count"

    override suspend fun fetchIngredientSummary(request: IngredientDepletionRequest): Pair<List<IngredientSummary>, Long> {
        // Get markets for the requested DC codes
        val markets = getMarketsForDcCodes(request.dcCodes)
        
        if (markets.isEmpty()) {
            return emptyList<IngredientSummary>() to 0L
        }

        // Fetch data and count in parallel
        val dataFuture = fetchIngredientSummaryData(markets, request)
        val countFuture = fetchIngredientSummaryCount(markets)

        val data = dataFuture.await()
        val count = countFuture.await()

        return data to count
    }

    private fun getMarketsForDcCodes(dcCodes: Set<String>): Set<String> {
        return dcCodes.mapNotNull { dcCode ->
            dcConfigService.dcConfigurations[dcCode]?.market
        }.toSet()
    }

    private fun fetchIngredientSummaryData(
        markets: Set<String>,
        request: IngredientDepletionRequest
    ) = metricsDSLContext.withTagName(fetchIngredientSummaryTag)
        .select(
            SKU_SPECIFICATION_VIEW.CODE,
            SKU_SPECIFICATION_VIEW.NAME,
            SKU_SPECIFICATION_VIEW.CATEGORY,
            SKU_SPECIFICATION_VIEW.UOM,
            SKU_SPECIFICATION_VIEW.MARKET
        )
        .from(SKU_SPECIFICATION_VIEW)
        .where(lower(SKU_SPECIFICATION_VIEW.MARKET).`in`(markets.map { lower(it) }))
        .orderBy(SKU_SPECIFICATION_VIEW.NAME)
        .offset(request.page * request.skuCount)
        .limit(request.skuCount)
        .fetchAsync()
        .thenApply { records ->
            records.flatMap { record ->
                // For each SKU, create entries for each DC in the matching market
                val market = record[SKU_SPECIFICATION_VIEW.MARKET]
                val dcCodesForMarket = getDcCodesForMarket(market, request.dcCodes)
                
                dcCodesForMarket.map { dcCode ->
                    IngredientSummary(
                        site = dcCode,
                        brand = determineBrand(market), // For now, map market to brand
                        skuCode = record[SKU_SPECIFICATION_VIEW.CODE],
                        skuName = record[SKU_SPECIFICATION_VIEW.NAME],
                        category = record[SKU_SPECIFICATION_VIEW.CATEGORY],
                        uom = mapUomToSkuUOM(record[SKU_SPECIFICATION_VIEW.UOM])
                    )
                }
            }
        }

    private fun fetchIngredientSummaryCount(markets: Set<String>) =
        metricsDSLContext.withTagName(fetchIngredientSummaryCountTag)
            .select(count())
            .from(SKU_SPECIFICATION_VIEW)
            .where(lower(SKU_SPECIFICATION_VIEW.MARKET).`in`(markets.map { lower(it) }))
            .fetchAsync()
            .thenApply { records ->
                records.firstOrNull()?.value1()?.toLong() ?: 0L
            }

    private fun getDcCodesForMarket(market: String, requestedDcCodes: Set<String>): List<String> {
        return dcConfigService.dcConfigurations.values
            .filter { it.market.equals(market, ignoreCase = true) && requestedDcCodes.contains(it.dcCode) }
            .map { it.dcCode }
    }

    private fun determineBrand(market: String): String {
        // Map market to brand - this is a simplified mapping
        // In a real implementation, this might come from a separate table or configuration
        return when (market.uppercase()) {
            "DACH" -> "HelloFresh"
            "US" -> "HelloFresh"
            "UK" -> "HelloFresh"
            "AU" -> "HelloFresh"
            "NZ" -> "HelloFresh"
            "CA" -> "HelloFresh"
            "FR" -> "HelloFresh"
            "IT" -> "HelloFresh"
            "ES" -> "HelloFresh"
            "BE" -> "HelloFresh"
            "NL" -> "HelloFresh"
            else -> "HelloFresh" // Default brand
        }
    }

    private fun mapUomToSkuUOM(uom: Uom): SkuUOM {
        return when (uom) {
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        }
    }
}
