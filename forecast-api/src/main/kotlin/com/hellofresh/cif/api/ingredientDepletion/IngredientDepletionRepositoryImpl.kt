package com.hellofresh.cif.api.ingredientDepletion

import com.hellofresh.cif.api.ingredientDepletion.model.IngredientDepletionRequest
import com.hellofresh.cif.api.ingredientDepletion.model.IngredientSummary
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_inputs_lib.schema.enums.Uom
import kotlinx.coroutines.future.await


class IngredientDepletionRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService
) : IngredientDepletionRepository {

    private val fetchIngredientSummaryTag = "fetch-ingredient-summary"

    override suspend fun fetchIngredientSummary(
        request: IngredientDepletionRequest
    ): List<IngredientSummary> {
        return metricsDSLContext.withTagName(fetchIngredientSummaryTag)
            .selectDistinct(
                SKU_SPECIFICATION_VIEW.CODE,
                SKU_SPECIFICATION_VIEW.NAME,
                SKU_SPECIFICATION_VIEW.CATEGORY,
                SKU_SPECIFICATION_VIEW.UOM,
                SKU_SPECIFICATION_VIEW.MARKET,
                CALCULATION.DC_CODE
            )
            .from(CALCULATION)
            .join(SKU_SPECIFICATION_VIEW).on(CALCULATION.CSKU_ID.eq(SKU_SPECIFICATION_VIEW.ID))
            .where(CALCULATION.DC_CODE.`in`(request.dcCodes))
            .fetchAsync()
            .thenApply { records ->
                records.map { record ->
                    IngredientSummary(
                        site = record[CALCULATION.DC_CODE],
                        brand = request.brand,
                        skuCode = record[SKU_SPECIFICATION_VIEW.CODE],
                        skuName = record[SKU_SPECIFICATION_VIEW.NAME],
                        category = record[SKU_SPECIFICATION_VIEW.CATEGORY],
                        uom = mapUomToSkuUOM(record[SKU_SPECIFICATION_VIEW.UOM])
                    )
                }
            }
            .await()
    }

    private fun mapUomToSkuUOM(uom: Uom): SkuUOM {
        return when (uom) {
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        }
    }
}
