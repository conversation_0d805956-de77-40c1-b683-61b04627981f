package com.hellofresh.cif.api

import com.hellofresh.cif.api.calculation.CalculationResponseMapper
import com.hellofresh.cif.api.calculation.calculationRoutingModule
import com.hellofresh.cif.api.calculation.csvexport.calculationCsvExportModule
import com.hellofresh.cif.api.calculation.experiment.CalculationExperimentService
import com.hellofresh.cif.api.calculation.experiment.calculationExperimentModule
import com.hellofresh.cif.api.calculation.projectedWaste.ProjectedWasteCalculationCsvConverter
import com.hellofresh.cif.api.calculation.projectedWaste.ProjectedWasteCalculationService
import com.hellofresh.cif.api.calculation.projectedWaste.projectedWasteModule
import com.hellofresh.cif.api.cleardown.CleardownTriggerRepositoryImpl
import com.hellofresh.cif.api.cleardown.cleardownTriggerModule
import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.configuration.configRoutingModule
import com.hellofresh.cif.api.demand.DemandRepositoryImpl
import com.hellofresh.cif.api.demand.DemandService
import com.hellofresh.cif.api.demand.demandByMarket
import com.hellofresh.cif.api.demand.demandInfoModule
import com.hellofresh.cif.api.fileexport.FileExportService
import com.hellofresh.cif.api.fileexport.fileExportModule
import com.hellofresh.cif.api.fileexport.repository.FileExportRequestRepositoryImpl
import com.hellofresh.cif.api.fileupload.FileUploadService
import com.hellofresh.cif.api.fileupload.fileUploadModule
import com.hellofresh.cif.api.fileupload.repository.FileUploadRepositoryImpl
import com.hellofresh.cif.api.ingredientDepletion.IngredientDepletionRepositoryImpl
import com.hellofresh.cif.api.ingredientDepletion.IngredientDepletionService
import com.hellofresh.cif.api.ingredientDepletion.ingredientDepletionModule
import com.hellofresh.cif.api.inventory.GenerateUploadUrlService
import com.hellofresh.cif.api.inventory.SkuInventoryDetailService
import com.hellofresh.cif.api.inventory.generateUploadUrlRoutingModule
import com.hellofresh.cif.api.inventory.inventoryRoutingModule
import com.hellofresh.cif.api.ktor.HttpServer
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.ServerReadyListener
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.ktor.docsRoutingModule
import com.hellofresh.cif.api.note.noteModule
import com.hellofresh.cif.api.po.PurchaseOrderService
import com.hellofresh.cif.api.po.purchaseOrderModule
import com.hellofresh.cif.api.reporting.StockReportingRepositoryImpl
import com.hellofresh.cif.api.reporting.StockReportingService
import com.hellofresh.cif.api.reporting.stockReportingRoutingModule
import com.hellofresh.cif.api.shortShelfLife.shortShelfLifeModule
import com.hellofresh.cif.api.stockupdate.StockUpdateApiService
import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.api.stockupdate.StockUpdateInputDataService
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateApiRepositoryImpl
import com.hellofresh.cif.api.stockupdate.stockUpdateModule
import com.hellofresh.cif.api.supplier.supplierRoutingModule
import com.hellofresh.cif.api.supplyQuantityRecommendation.SQRConfigService
import com.hellofresh.cif.api.supplyQuantityRecommendation.SupplyQuantityRecommendationService
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SQRConfigRepositoryImpl
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.StockUpdatesReadRepositoryImpl
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendationRepositoryImpl
import com.hellofresh.cif.api.supplyQuantityRecommendation.supplyQuantityRecommendationModule
import com.hellofresh.cif.api.to.TransferOrderService
import com.hellofresh.cif.api.to.transferOrderModule
import com.hellofresh.cif.api.warmup.ForecastApiWarmup
import com.hellofresh.cif.calculator.CalculatorClient
import com.hellofresh.cif.checks.AsyncWarmupCheck
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.checks.Warmup
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.db.DBConfiguration.jooqMasterDslContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.inventory.InventoryActivityRepositoryImpl
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.LiveInventoryRepositoryImpl
import com.hellofresh.cif.inventory.StockUpdateRepositoryImpl
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepositoryImpl
import com.hellofresh.cif.safetystock.repository.SafetyStockConfigurationRepository
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.skuinput.repo.SupplierRepositoryImpl
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.cif.transferorder.db.TransferOrderRepositoryImpl
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import io.micrometer.core.instrument.MeterRegistry
import kotlin.time.Duration

internal const val HTTP_PORT = 8080
private const val HTTP_ACTUATOR = 8081

@Suppress("LongMethod")
suspend fun main() {
    val meterRegistry = createMeterRegistry()
    val asyncWarmupCheck = createAsyncWarmupCheck(meterRegistry)
    runStatusServer(meterRegistry, asyncWarmupCheck)

    val readOnlyDslContext = DBConfiguration.jooqReadOnlyDslContext(
        readDbConnections(),
        meterRegistry,
    )
    val readWriteDslContext = jooqMasterDslContext(
        masterDbConnections(),
        meterRegistry,
    )
    val statsigFeatureFlagClient = statsigFeatureFlagClient()
    val dcConfigService = DcConfigService(meterRegistry)
    val purchaseOrderRepository = PurchaseOrderRepositoryImpl(
        readOnlyDslContext,
        dcConfigService,
        statsigFeatureFlagClient,
    )
    val transferOrderRepository = TransferOrderRepositoryImpl(readOnlyDslContext, dcConfigService)
    val stockReportingRepository = StockReportingRepositoryImpl(readOnlyDslContext)
    val fileExportRequestRepository = FileExportRequestRepositoryImpl(readWriteDslContext)
    val usableInventoryEvaluator = UsableInventoryEvaluator(statsigFeatureFlagClient)

    val noteService = noteService(
        readWriteDslContext,
        readOnlyDslContext,
    )
    val calculationsCsvConverter = calculationsCsvConverter(dcConfigService, statsigFeatureFlagClient)
    val fileExportService = FileExportService(fileExportRequestRepository)

    val skuInputDataRepository = SkuInputDataRepositoryImpl(readOnlyDslContext, dcConfigService)
    val inventoryRepository = InventoryRepositoryImpl(readOnlyDslContext)
    val liveInventoryRepository = LiveInventoryRepositoryImpl(readOnlyDslContext)

    val inventoryService =
        InventoryService(
            inventoryRepository,
            liveInventoryRepository,
            InventoryActivityRepositoryImpl(readOnlyDslContext),
            statsigFeatureFlagClient,
        )
    val calculatorClient = CalculatorClient(statsigFeatureFlagClient)
    val demandDomainRepository = com.hellofresh.cif.demand.DemandRepositoryImpl(
        readOnlyDslContext,
        statsigFeatureFlagClient,
    )
    val stockUpdateInputDataService = StockUpdateInputDataService(
        skuInputDataRepository,
        inventoryService,
        purchaseOrderRepository,
        transferOrderRepository,
        demandDomainRepository,
        SafetyStockRepository(readOnlyDslContext),
        preproductionCleardownDcs(),
    )
    val stockUpdateCalculationService =
        StockUpdateCalculationService(
            calculatorClient,
            stockUpdateInputDataService,
        )

    val stockUpdateApiService = StockUpdateApiService(
        StockUpdateService(
            inventoryService,
            StockUpdateRepositoryImpl(readOnlyDslContext),
        ),
        stockUpdateCalculationService,
        StockUpdateApiRepositoryImpl(dcConfigService, readOnlyDslContext, readWriteDslContext),
        dcConfigService,
        skuInputDataRepository,
    )

    val stockUpdateService = StockUpdateService(inventoryService, StockUpdateRepositoryImpl(readOnlyDslContext))
    val sqrShortShelfLifeConfRepository = SQRShortShelfLifeConfRepository(readOnlyDslContext)
    val shortShelfLifeService = shortShelfLifeService(
        readWriteDslContext,
        readOnlyDslContext,
        stockUpdateApiService,
        dcConfigService,
        stockUpdateCalculationService,
        sqrShortShelfLifeConfRepository = sqrShortShelfLifeConfRepository,
        stockUpdateService = stockUpdateService,
    )
    val calculationsService = createCalculationService(
        meterRegistry,
        dcConfigService,
        readOnlyDslContext,
        usableInventoryEvaluator,
        stockUpdateApiService,
        statsigFeatureFlagClient,
    )
    val calculationCsvExportService = calculationCsvFileService(
        fileExportService,
        calculationsService,
        purchaseOrderRepository,
        dcConfigService,
        calculationsCsvConverter,
        meterRegistry,
    )

    val configService = ConfigService(dcConfigService, inventoryService)
    val purchaseOrderService = PurchaseOrderService(purchaseOrderRepository, configService)
    val stockReportingService = StockReportingService(stockReportingRepository, purchaseOrderService)
    val skuInventoryDetailService =
        SkuInventoryDetailService(
            dcConfigService,
            inventoryRepository,
            skuInputDataRepository,
            statsigFeatureFlagClient,
        )

    val calculationExperimentService = CalculationExperimentService(stockUpdateCalculationService, dcConfigService)

    val demandService = DemandService(DemandRepositoryImpl(readOnlyDslContext, dcConfigService))
    val timeoutDuration = Duration.parse(ConfigurationLoader.getStringOrDefault("db.timeout", "PT15S"))
    val calculationResponseMapper = CalculationResponseMapper(statsigFeatureFlagClient)

    val supplierRepository = SupplierRepositoryImpl(readOnlyDslContext)
    val projectedWasteService = ProjectedWasteCalculationService(
        meterRegistry,
        readOnlyDslContext,
        dcConfigService,
        statsigFeatureFlagClient,
    )
    val cleardownTriggerRepository = CleardownTriggerRepositoryImpl(readWriteDslContext, dcConfigService)
    val sqrConfigRepository = SQRConfigRepositoryImpl(
        readWriteDslContext,
    )
    val sqrConfigService = SQRConfigService(
        sqrConfigRepository,
    )
    val fileUploadRepository = FileUploadRepositoryImpl(readOnlyDslContext)
    val fileUploadService = FileUploadService(fileUploadRepository)
    val transferOrderService = TransferOrderService(transferOrderRepository, configService)

    val ingredientDepletionRepository = IngredientDepletionRepositoryImpl(readOnlyDslContext, dcConfigService)
    val ingredientDepletionService = IngredientDepletionService(ingredientDepletionRepository)

    HttpServer.start(
        HTTP_PORT,
        meterRegistry,
        listOf(
            { configureJwtAuth(getJwtCredentials(), !ConfigurationLoader.isLocal()) },
            configRoutingModule(configService, timeoutDuration),
            calculationRoutingModule(
                calculationsService,
                calculationResponseMapper,
                timeoutDuration,
            ),
            calculationExperimentModule(
                calculationExperimentService,
                timeoutDuration,
            ),
            projectedWasteModule(
                projectedWasteService,
                ProjectedWasteCalculationCsvConverter(),
                timeoutDuration,
            ),
            noteModule(noteService, timeoutDuration),
            shortShelfLifeModule(
                shortShelfLifeService,
                timeoutDuration,
            ),
            fileExportModule(fileExportService, timeoutDuration),
            calculationCsvExportModule(calculationCsvExportService, timeoutDuration),
            stockUpdateModule(
                stockUpdateApiService,
                statsigFeatureFlagClient,
                usableInventoryEvaluator,
                timeoutDuration,
            ),
            stockReportingRoutingModule(stockReportingService, timeoutDuration),
            inventoryRoutingModule(skuInventoryDetailService, timeoutDuration),
            generateUploadUrlRoutingModule(GenerateUploadUrlService(getS3FileService(), getS3BucketSuffix())),
            demandInfoModule(demandService, timeoutDuration),
            demandByMarket(demandService, timeoutDuration),
            cleardownTriggerModule(cleardownTriggerRepository, timeoutDuration),
            purchaseOrderModule(purchaseOrderService, dcConfigService, timeoutDuration),
            docsRoutingModule(),
            supplierRoutingModule(supplierRepository, dcConfigService, timeoutDuration),
            supplyQuantityRecommendationModule(
                configService,
                sqrConfigService,
                SupplyQuantityRecommendationService(
                    SupplyQuantityRecommendationRepositoryImpl(readOnlyDslContext, dcConfigService),
                    StockUpdatesReadRepositoryImpl(readOnlyDslContext),
                    SupplyQuantityRecommendationConfigRepository(readOnlyDslContext),
                    SafetyStockRepository(readOnlyDslContext),
                    SafetyStockConfigurationRepository(readOnlyDslContext),
                    dcConfigService,
                ),
                timeoutDuration,
            ),
            fileUploadModule(fileUploadService, timeoutDuration),
            transferOrderModule(transferOrderService, dcConfigService, timeoutDuration),
            ingredientDepletionModule(ingredientDepletionService, timeoutDuration),
        ),
        serverReadyListeners = listOf(ServerReadyListener { asyncWarmupCheck.fireAndForget() }),
    )
}

private fun statsigFeatureFlagClient() = StatsigFactory.build(
    ::shutdownHook,
    sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
    userId = ConfigurationLoader.getStringOrFail("application.name"),
    isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
    hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
)

private fun runStatusServer(meterRegistry: HelloFreshMeterRegistry, asyncWarmupCheck: AsyncWarmupCheck) {
    StatusServer.run(
        meterRegistry = meterRegistry,
        port = HTTP_ACTUATOR,
        startUpCheck = StartUpChecks.add(asyncWarmupCheck),
    )
}

private fun getJwtCredentials() = JwtCredentials(
    ConfigurationLoader.getStringOrFail("HF_AUTH_SERVICE_JWT_SECRET_KEY"),
    ConfigurationLoader.getApplicationName(),
    ConfigurationLoader.getStringOrFail("HF_AZURE_ISSUER"),
    ConfigurationLoader.getStringOrFail("HF_AZURE_CLIENT_ID"),
    ConfigurationLoader.getStringOrFail("HF_AZURE_JWKS_URI"),
)

private fun readDbConnections() = ConfigurationLoader.getIntegerOrDefault("db.connections", 1)
private fun masterDbConnections() = ConfigurationLoader.getIntegerOrDefault("db.master.connections", 1)

private fun createAsyncWarmupCheck(meterRegistry: MeterRegistry) =
    AsyncWarmupCheck(
        "ForecastApiWarmup",
        if (isWarmupEnabled()) {
            ForecastApiWarmup(getJwtCredentials(), meterRegistry)
        } else {
            Warmup {}
        },
    )

private fun isWarmupEnabled() = ConfigurationLoader.getStringOrDefault("boot.warmup", "true").toBoolean()

private fun preproductionCleardownDcs() = ConfigurationLoader.getStringOrFail(
    "preproduction.cleardown.dcs",
).split(",").toSet()
