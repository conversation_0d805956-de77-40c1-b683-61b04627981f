openapi: 3.0.3
servers: [ ]
info:
  description: API to fetch SKU Inventory forecast
  version: 1.0.0
  title: Calculation API
  contact:
    email: <EMAIL>
    url: https://slack.com/app_redirect?channel=squad-inventory
paths:
  /config/{country}:
    get:
      operationId: getCountryConfig
      tags:
          - config
      summary: 'Fetch configurations for all the DCs in the market in which the country lies.'
      parameters:
        - name: country
          in: path
          required: true
          schema:
            $ref: 'components.yaml#/components/schemas/CountryResponse'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/Configuration'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '404':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /calculation/filters:
    get:
      operationId: getCalculationsFiltersConfig
      tags:
          - calculation
      summary: 'Fetch filter metadata for given calculations params.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/AcceptHeader'
        - $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
        - $ref: 'components.yaml#/components/parameters/inventoryRefreshParam'
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/skuCodesParam'
        - $ref: 'components.yaml#/components/parameters/skuCategoriesParam'
        - $ref: 'components.yaml#/components/parameters/additionalFiltersParam'
        - $ref: 'components.yaml#/components/parameters/locationInBoxParam'
        - $ref: 'components.yaml#/components/parameters/supplierIdParam'
        - $ref: 'components.yaml#/components/parameters/activeSuppliersNameParam'
        - $ref: 'components.yaml#/components/parameters/poDueInLessThanOrEqual'
        - $ref: 'components.yaml#/components/parameters/poDueInGreaterThanOrEqual'
        - $ref: 'components.yaml#/components/parameters/closingStockLessThanOrEqual'

      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/CalculationFiltersResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '404':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /calculation/weeklyView:
    get:
      tags:
          - calculation
      summary: 'Fetch one per week calculations for all skus in the requested DCs and matching the filters'
      parameters:
        - $ref: 'components.yaml#/components/parameters/AcceptHeader'
        - $ref: 'components.yaml#/components/parameters/sortByParam'
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/pageParam'
        - $ref: 'components.yaml#/components/parameters/skuCountParam'
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/skuCodesParam'
        - $ref: 'components.yaml#/components/parameters/skuCategoriesParam'
        - $ref: 'components.yaml#/components/parameters/additionalFiltersParam'
        - $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
        - $ref: 'components.yaml#/components/parameters/locationInBoxParam'
        - $ref: 'components.yaml#/components/parameters/inventoryRefreshParam'
        - $ref: 'components.yaml#/components/parameters/supplierIdParam'
        - $ref: 'components.yaml#/components/parameters/poDueInLessThanOrEqual'
        - $ref: 'components.yaml#/components/parameters/poDueInGreaterThanOrEqual'
        - $ref: 'components.yaml#/components/parameters/closingStockLessThanOrEqual'

      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/WeeklyCalculationsResponse'
            text/csv:
              schema:
                type: string
        '400':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /calculation/dailyView:
    get:
      tags:
        - calculation
      summary: 'Fetch daily calculations for all skus in the requested DCs and matching the filters.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/AcceptHeader'
        - $ref: 'components.yaml#/components/parameters/sortByParam'
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/pageParam'
        - $ref: 'components.yaml#/components/parameters/skuCountParam'
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/skuCodesParam'
        - $ref: 'components.yaml#/components/parameters/skuCategoriesParam'
        - $ref: 'components.yaml#/components/parameters/additionalFiltersParam'
        - $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
        - $ref: 'components.yaml#/components/parameters/locationInBoxParam'
        - $ref: 'components.yaml#/components/parameters/inventoryRefreshParam'
        - $ref: 'components.yaml#/components/parameters/supplierIdParam'
        - $ref: 'components.yaml#/components/parameters/poDueInLessThanOrEqual'
        - $ref: 'components.yaml#/components/parameters/poDueInGreaterThanOrEqual'
        - $ref: 'components.yaml#/components/parameters/closingStockLessThanOrEqual'

      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/DailyCalculationsResponse'
            text/csv:
              schema:
                type: string
        '400':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /calculation/experiment/{dcCode}/{skuId}:
    post:
      operationId: createCalculationExperiment
      tags:
          - calculation
      summary: 'Create a calculation experiment.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodePathParam'
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/skuIdParam'
        - $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: 'components.yaml#/components/schemas/CalculationExperimentRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/DailyCalculationExperimentsResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /calculation/experiment/{skuId}:
    post:
      operationId: createCalculationExperimentWithDcCodeQueryParam
      tags:
          - calculation
      summary: 'Create a calculation experiment.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/skuIdParam'
        - $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: 'components.yaml#/components/schemas/CalculationExperimentRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/DailyCalculationExperimentsResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /calculation/experiment/{skuId}/weeklyView:
    post:
      operationId: createCalculationWeeklyExperiment
      tags:
          - calculation
      summary: 'Create calculation for the weekly experiment.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/skuIdParam'
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: 'components.yaml#/components/schemas/CalculationWeeklyExperimentsRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/WeeklyCalculationExperimentsResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '400':
          description: Multiple DCs are not supported
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /sku/{skuId}/detail:
    get:
      operationId: getSkuDetail
      tags:
          - sku
      summary: 'Fetch details such as useByDate, isUsable, quantity for given skuCode.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/skuIdParam'
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/SkuDetailsResponse'
        '500':
          description: Unhandled error by Server.
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /generate-upload-url/{market}:
      get:
          operationId: generateUploadUrl
          summary: 'Get a pre-signed URL for uploading a file to S3.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/marketParam'
              -   $ref: 'components.yaml#/components/parameters/fileTypeQueryParam'
              -   name: 'fileName'
                  in: query
                  required: true
                  allowEmptyValue: false
                  schema:
                    type: string
              -   name: 'authorName'
                  in: query
                  required: true
                  allowEmptyValue: false
                  schema:
                      type: string
              -   name: 'authorEmail'
                  in: query
                  required: true
                  allowEmptyValue: false
                  schema:
                      type: string
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/GenerateUploadUrlResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /dc/{dcCode}/stockVariance:
    get:
      operationId: getStockVariance
      tags:
          - dc
      summary: 'List all products where last week’s closing stock doesn’t match this week’s opening stock.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodePathParam'
        - name: 'cleardownDate'
          in: query
          required: true
          allowEmptyValue: false
          schema:
            type: string
            format: date
            example: 2022-12-31
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/StockVarianceResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unhandled error by Server.
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /dc/{dcCode}/liveVariance:
    get:
      operationId: getStockLiveVariance
      tags:
          - dc
      summary: 'List all the predicted usable quantities from default, live and inventory view.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodePathParam'
        - name: 'week'
          in: query
          required: true
          allowEmptyValue: false
          schema:
              $ref: 'components.yaml#/components/schemas/YearWeek'

      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/StockLiveVarianceReportingResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unhandled error by Server.
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /dc/{dcCode}/inboundVariance:
    get:
      operationId: getStockInboundVariance
      tags:
          - dc
      summary: 'List all the PO vs Inbound, ASN vs Inbound variances.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodePathParam'
        - name: 'week'
          in: query
          required: true
          allowEmptyValue: false
          schema:
            $ref: 'components.yaml#/components/schemas/YearWeek'

      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/StockInboundVarianceReportingResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unhandled error by Server.
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /notes:
    get:
      operationId: getNotes
      tags:
          - note
      summary: fetches all notes for the requested DCs, DC weeks and the currently authenticated user
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/skuIdsParam'
        - $ref: 'components.yaml#/components/parameters/skuCategoriesParam'
        - $ref: 'components.yaml#/components/parameters/additionalFiltersParam'
        - $ref: 'components.yaml#/components/parameters/locationInBoxParam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/GetNotesResponse'
    post:
      operationId: createNote
      summary: creates a note for the currently authenticated user
      tags:
          - note
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: 'components.yaml#/components/schemas/CreateNoteRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/CreateNoteResponse'

  /notes/{noteId}:
    put:
      operationId: updateNote
      tags:
          - note
      summary: updates a note
      parameters:
        - $ref: 'components.yaml#/components/parameters/noteIdPathParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: 'components.yaml#/components/schemas/UpdateNoteRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/CreateNoteResponse'
    delete:
      operationId: deleteNote
      summary: deletes a note
      tags:
          - note
      parameters:
        - $ref: 'components.yaml#/components/parameters/noteIdPathParam'
      responses:
        '204':
          description: note is deleted
        '404':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /forecast:
    get:
      operationId: getDemandRefreshTimestamps
      summary: retrieves the latest demand timestamp for each DC week in the range (current DC week - 4 weeks) - (current DC week + 9 weeks)
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/weeksParam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/DemandsRefreshResponse'
  /demand/{market}:
      get:
          operationId: getDemandByWeeks
          summary: fetches demand for the requested weeks and market
          parameters:
              - $ref: 'components.yaml#/components/parameters/marketParam'
              - $ref: 'components.yaml#/components/parameters/weeksParam'
              - name: 'timestamp'
                in: query
                required: false
                schema:
                    type: string
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/DemandPerWeeksResponse'
              '500':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'
              '404':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /purchase-order:
    get:
      operationId: getPurchaseOrders
      summary: 'Retrieves purchase orders for the requested weeks and containing grns and asns for the requested skuId only'
      parameters:
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/skuIdParam'
        - name: 'fromDate'
          in: query
          required: false
          allowEmptyValue: false
          description: 'Together with toDate define a range of dates. When fromDate and toDate query params exist, the weeks query params are ignored.'
          schema:
            type: string
            format: date
            example: 2022-12-31
        - name: 'toDate'
          in: query
          required: false
          allowEmptyValue: false
          schema:
            type: string
            format: date
            example: 2022-12-31
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/PurchaseOrdersDetailResponse'
        '404':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /suppliers/{dcCode}/{skuId}:
    get:
      operationId: getSupplierLeadTime
      summary: 'Fetch supplier lead time for the given sku id.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodePathParam'
        - $ref: 'components.yaml#/components/parameters/skuIdParam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/SupplierLeadTimesResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '404':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /inventory/cleardownTrigger:
    post:
      operationId: createInventoryCleardownTrigger
      summary: 'Create an inventory cleardown trigger for a DC.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/snapShotIdQueryParam'
      responses:
        '200':
          description: successful operation
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '404':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /projectedWaste:
    get:
      operationId: getProjectedWaste
      summary: 'Fetch projected waste for the requested DC/DCs.'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/pageParam'
        - $ref: 'components.yaml#/components/parameters/skuCodesParam'
        - $ref: 'components.yaml#/components/parameters/skuCategoriesParam'
        - $ref: 'components.yaml#/components/parameters/additionalFiltersParam'
        - $ref: 'components.yaml#/components/parameters/expiringInLessThanOrEqual'
        - $ref: 'components.yaml#/components/parameters/expiringInGreaterThanOrEqual'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/ProjectedWastesResponse'
            text/csv:
                schema:
                    type: string
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '404':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /stock-updates/current/{dcCode}/{skuId}:
      get:
          operationId: getStockUpdates
          tags:
              - stock_update
          summary: 'List of stock updates for the given DC and SKU.'
          parameters:
              - $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              - $ref: 'components.yaml#/components/parameters/skuIdParam'
              - $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/SkuStockUpdatesResponse'
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /stock-updates/{dcCode}/{week}:
      get:
          operationId: getAllStockUpdates
          tags:
              - stock_update
          summary: 'List of all stock updates for the given DC and SKU and week.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -   $ref: 'components.yaml#/components/parameters/weekPathParam'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/StockUpdatesVersionsResponse'
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
  /stock-updates/{dcCode}/{skuId}:
      post:
          operationId: createStockUpdates
          tags:
              - stock_update
          summary: 'Create a stock update for the given DC and SKU.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -   $ref: 'components.yaml#/components/parameters/skuIdParam'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          $ref: 'components.yaml#/components/schemas/StockUpdateRequest'
          responses:
              '201':
                  description: successful creation


  /stock-updates/simulation/{dcCode}/{skuId}:
      post:
          operationId: simulateStockUpdates
          tags:
              - stock_update
          summary: 'Simulation of stock updates for the given DC and SKU.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -   $ref: 'components.yaml#/components/parameters/skuIdParam'
              -   $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          $ref: 'components.yaml#/components/schemas/StockSimulationRequest'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/StockSimulationResponse'
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'


  /stock-updates/v2/current/{dcCode}/{skuId}:
      get:
          operationId: CurrentStockUpdates
          summary: 'Current stock updates for allowed days to update/insert for the given DC and SKU'
          tags:
              - simulation
          parameters:
              -  $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -  $ref: 'components.yaml#/components/parameters/skuIdParam'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/CurrentSkuStockUpdatesResponse'
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /stock-updates/v2/simulation/{dcCode}/{skuId}:
      post:
          operationId: createStockUpdateSimulationV2
          summary: 'Stock update simulation with stock update details.'
          tags:
              - simulation
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -   $ref: 'components.yaml#/components/parameters/weeksParam'
              -   $ref: 'components.yaml#/components/parameters/skuIdParam'
              -   $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          $ref: 'components.yaml#/components/schemas/CalculationExperimentV2Request'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/StockUpdateSimulationResponse'
              '500':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /stock-updates/v2/{dcCode}/{skuId}:
      post:
          operationId: createStockUpdatesV2
          summary: 'Create a stock update record for the given DC and SKU and return a simulation response.'
          tags:
              - simulation
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -   $ref: 'components.yaml#/components/parameters/skuIdParam'
              -   $ref: 'components.yaml#/components/parameters/weeksParamRequired'
              -   $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          $ref: 'components.yaml#/components/schemas/StockUpdateRequest'
          responses:
              '201':
                  description: successful creation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/StockUpdateV2Response'

  /supply-quantity-recommendation/config:
      post:
          operationId: createSQRConfiguration
          summary: 'Create the supply quantity recommendation configurations.'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          $ref: 'components.yaml#/components/schemas/SupplyQuantityRecommendationRequest'
          responses:
              '200':
                  description: successful operation
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /export/{requestId}:
      get:
          operationId: getStockOverviewExport
          tags:
              - export
          summary: 'get the status of stock overview export or return the file url if available'
          parameters:
              - name: 'requestId'
                in: path
                required: true
                allowEmptyValue: false
                schema:
                    type: string
                    format: uuid
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/FileExportResponse'
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '404':
                  description: Not Found
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /export/calculation/dailyView:
      get:
          operationId: createDailyCalculationExport dailyView
          tags:
              - export
          summary: 'Create a stock overview export dailyView.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/sortByParam'
              -   $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
              -   $ref: 'components.yaml#/components/parameters/weeksParam'
              -   $ref: 'components.yaml#/components/parameters/skuCodesParam'
              -   $ref: 'components.yaml#/components/parameters/skuCategoriesParam'
              -   $ref: 'components.yaml#/components/parameters/additionalFiltersParam'
              -   $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
              -   $ref: 'components.yaml#/components/parameters/inventoryRefreshParam'
              -   $ref: 'components.yaml#/components/parameters/locationInBoxParam'
              -   $ref: 'components.yaml#/components/parameters/supplierIdParam'
              -   $ref: 'components.yaml#/components/parameters/poDueInLessThanOrEqual'
              -   $ref: 'components.yaml#/components/parameters/poDueInGreaterThanOrEqual'
              -   $ref: 'components.yaml#/components/parameters/closingStockLessThanOrEqual'

          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/FileExportResponse'
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /export/calculation/weeklyView:
      get:
          operationId: createWeeklyCalculationExport
          tags:
              - export
          summary: 'Create a stock overview export weeklyView.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/sortByParam'
              -   $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
              -   $ref: 'components.yaml#/components/parameters/weeksParam'
              -   $ref: 'components.yaml#/components/parameters/skuCodesParam'
              -   $ref: 'components.yaml#/components/parameters/skuCategoriesParam'
              -   $ref: 'components.yaml#/components/parameters/additionalFiltersParam'
              -   $ref: 'components.yaml#/components/parameters/consumptionDaysAheadParam'
              -   $ref: 'components.yaml#/components/parameters/inventoryRefreshParam'
              -   $ref: 'components.yaml#/components/parameters/locationInBoxParam'
              -   $ref: 'components.yaml#/components/parameters/supplierIdParam'
              -   $ref: 'components.yaml#/components/parameters/poDueInLessThanOrEqual'
              -   $ref: 'components.yaml#/components/parameters/poDueInGreaterThanOrEqual'
              -   $ref: 'components.yaml#/components/parameters/closingStockLessThanOrEqual'

          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/FileExportResponse'
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /supply-quantity-recommendation:
      get:
          operationId: getSupplyQuantityRecommendation
          summary: 'Get the list of supply quantity recommendation.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodeQueryParam'
              -   $ref: 'components.yaml#/components/parameters/weekQueryParam'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/supplyQuantityRecommendationResponse'
              '500':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'
              '404':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /file-uploads:
      get:
          operationId: getFileUploads
          summary: 'Get the list of uploaded files'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/marketQueryParam'
              -   $ref: 'components.yaml#/components/parameters/fileTypeQueryParam'
          responses:
              '200':
                  description: Successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/fileUploadResponse'
              '400':
                  description: Bad Request
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'
              '500':
                  description: Unhandled error by Server.
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/ErrorResponse'

  /sqr-short-shelf-life/{dcCode}:
      get:
          operationId: getShortShelfLifeSQR
          tags:
              - ssl
          summary: 'Get the list of short shelf life SQRs.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -   $ref: 'components.yaml#/components/parameters/weekQueryParam'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/shortShelfLifeResponse'
              '500':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'
              '404':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /sqr-short-shelf-life/{dcCode}/{skuId}:
      put:
          operationId: putShortShelfLifeSQR
          tags:
              - ssl
          summary: 'Update / upsert the list of short shelf life SQRs and stockUpdates.'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -   $ref: 'components.yaml#/components/parameters/skuIdParam'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          $ref: 'components.yaml#/components/schemas/shortShelfLifeUpdateRequest'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/shortShelfLifePutResponse'
              '500':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'
              '404':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /sqr-short-shelf-life/simulation/{dcCode}/{skuId}:
      post:
          operationId: simulateShortShelfLifeSQR
          tags:
              - ssl
          summary: 'Simulate short shelf life SQRs values'
          parameters:
              -   $ref: 'components.yaml#/components/parameters/dcCodePathParam'
              -   $ref: 'components.yaml#/components/parameters/skuIdParam'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          $ref: 'components.yaml#/components/schemas/sslSqrSimulationRequest'
          responses:
              '200':
                  description: successful operation
                  content:
                      application/json:
                          schema:
                              $ref: 'components.yaml#/components/schemas/shortShelfLifeSimulationResponse'
              '500':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'
              '404':
                  $ref: 'components.yaml#/components/responses/apiErrorResponse'

  /transfer-order:
    get:
      operationId: getTransferOrders
      summary: 'Retrieves transfer orders for the requested weeks and for the requested skuId'
      parameters:
        - $ref: 'components.yaml#/components/parameters/weeksParam'
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/skuIdParam'
        - name: 'fromDate'
          in: query
          required: false
          allowEmptyValue: false
          description: 'Together with toDate define a range of dates. When fromDate and toDate query params exist, the weeks query params are ignored.'
          schema:
            type: string
            format: date
            example: 2022-12-31
        - name: 'toDate'
          in: query
          required: false
          allowEmptyValue: false
          schema:
            type: string
            format: date
            example: 2022-12-31
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/TransferOrdersDetailResponse'
        '404':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
  /ingredientDepletion:
    get:
      operationId: getIngredientDepletionView
      tags:
        - ingredient-depletion
      summary: 'Fetch ingredient depletion view data with ingredient summary'
      parameters:
        - $ref: 'components.yaml#/components/parameters/dcCodesQueryParam'
        - $ref: 'components.yaml#/components/parameters/pageParam'
        - $ref: 'components.yaml#/components/parameters/skuCountParam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'components.yaml#/components/schemas/IngredientDepletionResponse'
        '400':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'
        '500':
          $ref: 'components.yaml#/components/responses/apiErrorResponse'

tags:
  - name: simulation
    description: "Stock Update Simulation API V2"
  - name: ssl
    description: "Short Shelf Life For Supply Quantity Recommendation API"
  - name: config
    description: "Configuration API"
  - name: calculation
    description: "Calculation API"
  - name: sku
    description: "SKU API"
  - name: dc
    description: "DC API"
  - name: stock_update
    description: "Stock Update API"
  - name: export
    description: "Export API"
  - name: note
    description: "Note API"
  - name: ingredient-depletion
    description: "Ingredient Depletion View API"
